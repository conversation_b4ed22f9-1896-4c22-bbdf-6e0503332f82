/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/projects/page";
exports.ids = ["app/projects/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2Fpage&page=%2Fprojects%2Fpage&appPaths=%2Fprojects%2Fpage&pagePath=private-next-app-dir%2Fprojects%2Fpage.tsx&appDir=%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2Fpage&page=%2Fprojects%2Fpage&appPaths=%2Fprojects%2Fpage&pagePath=private-next-app-dir%2Fprojects%2Fpage.tsx&appDir=%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/projects/page.tsx */ \"(rsc)/./src/app/projects/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'projects',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Sites/nextjs_erp/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/projects/page\",\n        pathname: \"/projects\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2Fpage&page=%2Fprojects%2Fpage&appPaths=%2Fprojects%2Fpage&pagePath=private-next-app-dir%2Fprojects%2Fpage.tsx&appDir=%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2F%40fontsource%2Fibm-plex-sans-arabic%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2F%40fontsource%2Fibm-plex-sans-arabic%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/QueryProvider.tsx */ \"(rsc)/./src/components/providers/QueryProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ToastProvider.tsx */ \"(rsc)/./src/components/providers/ToastProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbXVoYW1tYWR5b3Vzc2VmJTJGU2l0ZXMlMkZuZXh0anNfZXJwJTJGbm9kZV9tb2R1bGVzJTJGJTQwZm9udHNvdXJjZSUyRmlibS1wbGV4LXNhbnMtYXJhYmljJTJGaW5kZXguY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbXVoYW1tYWR5b3Vzc2VmJTJGU2l0ZXMlMkZuZXh0anNfZXJwJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZtdWhhbW1hZHlvdXNzZWYlMkZTaXRlcyUyRm5leHRqc19lcnAlMkZzcmMlMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzJTJGUXVlcnlQcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJRdWVyeVByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbXVoYW1tYWR5b3Vzc2VmJTJGU2l0ZXMlMkZuZXh0anNfZXJwJTJGc3JjJTJGY29tcG9uZW50cyUyRnByb3ZpZGVycyUyRlRvYXN0UHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVG9hc3RQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb01BQTBKO0FBQzFKO0FBQ0Esb01BQTBKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJRdWVyeVByb3ZpZGVyXCJdICovIFwiL1VzZXJzL211aGFtbWFkeW91c3NlZi9TaXRlcy9uZXh0anNfZXJwL3NyYy9jb21wb25lbnRzL3Byb3ZpZGVycy9RdWVyeVByb3ZpZGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVG9hc3RQcm92aWRlclwiXSAqLyBcIi9Vc2Vycy9tdWhhbW1hZHlvdXNzZWYvU2l0ZXMvbmV4dGpzX2VycC9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvVG9hc3RQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2F%40fontsource%2Fibm-plex-sans-arabic%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fprojects%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fprojects%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/projects/page.tsx */ \"(rsc)/./src/app/projects/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbXVoYW1tYWR5b3Vzc2VmJTJGU2l0ZXMlMkZuZXh0anNfZXJwJTJGc3JjJTJGYXBwJTJGcHJvamVjdHMlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQXNHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbXVoYW1tYWR5b3Vzc2VmL1NpdGVzL25leHRqc19lcnAvc3JjL2FwcC9wcm9qZWN0cy9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fprojects%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tdWhhbW1hZHlvdXNzZWYvU2l0ZXMvbmV4dGpzX2VycC9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8f3aecd8ab37\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvbXVoYW1tYWR5b3Vzc2VmL1NpdGVzL25leHRqc19lcnAvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjhmM2FlY2Q4YWIzN1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fontsource_ibm_plex_sans_arabic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fontsource/ibm-plex-sans-arabic */ \"(rsc)/./node_modules/@fontsource/ibm-plex-sans-arabic/index.css\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_QueryProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/QueryProvider */ \"(rsc)/./src/components/providers/QueryProvider.tsx\");\n/* harmony import */ var _components_providers_ToastProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/ToastProvider */ \"(rsc)/./src/components/providers/ToastProvider.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"متبرمج - نظام إدارة المشاريع\",\n    description: \"نظام إدارة شامل للمشاريع والعملاء باللغة العربية\",\n    keywords: \"إدارة المشاريع, ERP, نظام إدارة, العربية, مشاريع, عملاء\",\n    authors: [\n        {\n            name: \"متبرمج\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-arabic antialiased bg-background text-text-primary\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_QueryProvider__WEBPACK_IMPORTED_MODULE_3__.QueryProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_ToastProvider__WEBPACK_IMPORTED_MODULE_4__.ToastProvider, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/projects/page.tsx":
/*!***********************************!*\
  !*** ./src/app/projects/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/providers/QueryProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/QueryProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const QueryProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Sites/nextjs_erp/src/components/providers/QueryProvider.tsx",
"QueryProvider",
);

/***/ }),

/***/ "(rsc)/./src/components/providers/ToastProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/ToastProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ToastProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ToastProvider() from the server but ToastProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Sites/nextjs_erp/src/components/providers/ToastProvider.tsx",
"ToastProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2F%40fontsource%2Fibm-plex-sans-arabic%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2F%40fontsource%2Fibm-plex-sans-arabic%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/QueryProvider.tsx */ \"(ssr)/./src/components/providers/QueryProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ToastProvider.tsx */ \"(ssr)/./src/components/providers/ToastProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbXVoYW1tYWR5b3Vzc2VmJTJGU2l0ZXMlMkZuZXh0anNfZXJwJTJGbm9kZV9tb2R1bGVzJTJGJTQwZm9udHNvdXJjZSUyRmlibS1wbGV4LXNhbnMtYXJhYmljJTJGaW5kZXguY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbXVoYW1tYWR5b3Vzc2VmJTJGU2l0ZXMlMkZuZXh0anNfZXJwJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZtdWhhbW1hZHlvdXNzZWYlMkZTaXRlcyUyRm5leHRqc19lcnAlMkZzcmMlMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzJTJGUXVlcnlQcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJRdWVyeVByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbXVoYW1tYWR5b3Vzc2VmJTJGU2l0ZXMlMkZuZXh0anNfZXJwJTJGc3JjJTJGY29tcG9uZW50cyUyRnByb3ZpZGVycyUyRlRvYXN0UHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVG9hc3RQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb01BQTBKO0FBQzFKO0FBQ0Esb01BQTBKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJRdWVyeVByb3ZpZGVyXCJdICovIFwiL1VzZXJzL211aGFtbWFkeW91c3NlZi9TaXRlcy9uZXh0anNfZXJwL3NyYy9jb21wb25lbnRzL3Byb3ZpZGVycy9RdWVyeVByb3ZpZGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVG9hc3RQcm92aWRlclwiXSAqLyBcIi9Vc2Vycy9tdWhhbW1hZHlvdXNzZWYvU2l0ZXMvbmV4dGpzX2VycC9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvVG9hc3RQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2F%40fontsource%2Fibm-plex-sans-arabic%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fprojects%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fprojects%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/projects/page.tsx */ \"(ssr)/./src/app/projects/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbXVoYW1tYWR5b3Vzc2VmJTJGU2l0ZXMlMkZuZXh0anNfZXJwJTJGc3JjJTJGYXBwJTJGcHJvamVjdHMlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQXNHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbXVoYW1tYWR5b3Vzc2VmL1NpdGVzL25leHRqc19lcnAvc3JjL2FwcC9wcm9qZWN0cy9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fprojects%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/projects/page.tsx":
/*!***********************************!*\
  !*** ./src/app/projects/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useProjects__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useProjects */ \"(ssr)/./src/hooks/useProjects.ts\");\n/* harmony import */ var _components_projects_ProjectsTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/projects/ProjectsTable */ \"(ssr)/./src/components/projects/ProjectsTable.tsx\");\n/* harmony import */ var _components_projects_ProjectsFilter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/projects/ProjectsFilter */ \"(ssr)/./src/components/projects/ProjectsFilter.tsx\");\n/* harmony import */ var _components_projects_ProjectForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/projects/ProjectForm */ \"(ssr)/./src/components/projects/ProjectForm.tsx\");\n/* harmony import */ var _components_layout_MainLayout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/MainLayout */ \"(ssr)/./src/components/layout/MainLayout.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _barrel_optimize_names_DocumentArrowDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentArrowDownIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentArrowDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentArrowDownIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n/**\n * Projects Page - متبرمج ERP System\n * Main projects management interface with table, filters, and CRUD operations\n */ \n\n\n\n\n\n\n\n\n// Modal Component\nfunction Modal({ isOpen, onClose, title, children }) {\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen items-center justify-center p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-25\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"إغلاق\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-6 w-6\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\nfunction ProjectsPage() {\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        page_size: 25,\n        ordering: '-start_date'\n    });\n    const [isCreateModalOpen, setIsCreateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditModalOpen, setIsEditModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProject, setSelectedProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // API Hooks\n    const { data: projectsData, isLoading, error, refetch } = (0,_hooks_useProjects__WEBPACK_IMPORTED_MODULE_2__.useProjects)(filters);\n    const createProjectMutation = (0,_hooks_useProjects__WEBPACK_IMPORTED_MODULE_2__.useCreateProject)();\n    const updateProjectMutation = (0,_hooks_useProjects__WEBPACK_IMPORTED_MODULE_2__.useUpdateProject)();\n    const deleteProjectMutation = (0,_hooks_useProjects__WEBPACK_IMPORTED_MODULE_2__.useDeleteProject)();\n    // Handle filter changes\n    const handleFiltersChange = (newFilters)=>{\n        setFilters((prev)=>({\n                ...prev,\n                ...newFilters,\n                page: 1\n            })) // Reset to page 1 when filters change\n        ;\n    };\n    const handleClearFilters = ()=>{\n        setFilters({\n            page: 1,\n            page_size: 25,\n            ordering: '-start_date'\n        });\n    };\n    // CRUD Operations\n    const handleCreateProject = async (data)=>{\n        try {\n            await createProjectMutation.mutateAsync(data);\n            setIsCreateModalOpen(false);\n            refetch();\n        } catch (error) {\n        // Error is handled by the mutation\n        }\n    };\n    const handleEditProject = (project)=>{\n        setSelectedProject(project);\n        setIsEditModalOpen(true);\n    };\n    const handleUpdateProject = async (data)=>{\n        if (!selectedProject) return;\n        try {\n            await updateProjectMutation.mutateAsync({\n                id: selectedProject.id,\n                data\n            });\n            setIsEditModalOpen(false);\n            setSelectedProject(null);\n            refetch();\n        } catch (error) {\n        // Error is handled by the mutation\n        }\n    };\n    const handleDeleteProject = async (project)=>{\n        if (!confirm(`هل أنت متأكد من حذف المشروع \"${project.name}\"؟`)) {\n            return;\n        }\n        try {\n            await deleteProjectMutation.mutateAsync(project.id);\n            refetch();\n        } catch (error) {\n        // Error is handled by the mutation\n        }\n    };\n    const handleViewProject = (project)=>{\n        // TODO: Navigate to project details page\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(`عرض تفاصيل المشروع: ${project.name}`);\n    };\n    const handleExportProjects = ()=>{\n        // TODO: Implement export functionality\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success('سيتم تنفيذ تصدير المشاريع قريباً');\n    };\n    // Handle API errors\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsPage.useEffect\": ()=>{\n            if (error) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('حدث خطأ في تحميل المشاريع');\n            }\n        }\n    }[\"ProjectsPage.useEffect\"], [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_MainLayout__WEBPACK_IMPORTED_MODULE_6__.MainLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"إدارة المشاريع\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-gray-500\",\n                                        children: \"إدارة شاملة لجميع المشاريع والعمليات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        variant: \"outline\",\n                                        onClick: handleExportProjects,\n                                        className: \"flex items-center space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"تصدير\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        onClick: ()=>setIsCreateModalOpen(true),\n                                        className: \"flex items-center space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"مشروع جديد\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_projects_ProjectsFilter__WEBPACK_IMPORTED_MODULE_4__.ProjectsFilter, {\n                            filters: filters,\n                            onFiltersChange: handleFiltersChange,\n                            onClearFilters: handleClearFilters\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        projectsData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-surface p-6 rounded-lg shadow-sm border border-border-light\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-text-secondary\",\n                                                        children: \"إجمالي المشاريع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-text-primary\",\n                                                        children: projectsData.count\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-primary-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-surface p-6 rounded-lg shadow-sm border border-border-light\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-text-secondary\",\n                                                        children: \"قيد التنفيذ\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-primary-600\",\n                                                        children: projectsData.results.filter((p)=>p.status === 'in_progress').length\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-primary-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-surface p-6 rounded-lg shadow-sm border border-border-light\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-text-secondary\",\n                                                        children: \"مكتملة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-accent-green-600\",\n                                                        children: projectsData.results.filter((p)=>p.status === 'completed').length\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-accent-green-50 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-accent-green-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-surface p-6 rounded-lg shadow-sm border border-border-light\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-text-secondary\",\n                                                        children: \"متأخرة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-accent-red-600\",\n                                                        children: projectsData.results.filter((p)=>new Date(p.expected_end_date) < new Date() && p.status !== 'completed').length\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-accent-red-50 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-accent-red-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-surface rounded-lg shadow-sm border border-border-light\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_projects_ProjectsTable__WEBPACK_IMPORTED_MODULE_3__.ProjectsTable, {\n                                projects: projectsData?.results || [],\n                                loading: isLoading,\n                                onEdit: handleEditProject,\n                                onDelete: handleDeleteProject,\n                                onView: handleViewProject\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Modal, {\n                isOpen: isCreateModalOpen,\n                onClose: ()=>setIsCreateModalOpen(false),\n                title: \"إنشاء مشروع جديد\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_projects_ProjectForm__WEBPACK_IMPORTED_MODULE_5__.ProjectForm, {\n                    onSubmit: handleCreateProject,\n                    onCancel: ()=>setIsCreateModalOpen(false),\n                    loading: createProjectMutation.isPending\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Modal, {\n                isOpen: isEditModalOpen,\n                onClose: ()=>{\n                    setIsEditModalOpen(false);\n                    setSelectedProject(null);\n                },\n                title: \"تعديل المشروع\",\n                children: selectedProject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_projects_ProjectForm__WEBPACK_IMPORTED_MODULE_5__.ProjectForm, {\n                    project: selectedProject,\n                    onSubmit: handleUpdateProject,\n                    onCancel: ()=>{\n                        setIsEditModalOpen(false);\n                        setSelectedProject(null);\n                    },\n                    loading: updateProjectMutation.isPending\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/projects/page.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/projects/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n/**\n * Header Component - متبرمج ERP System\n * Top navigation bar with menu toggle and user actions\n */ \n\n\nfunction Header({ onMenuToggle }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-surface shadow-sm border-b border-border-light\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4 space-x-reverse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: onMenuToggle,\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-text-tertiary\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"البحث...\",\n                                        className: \"w-64 pr-10 pl-3 py-2 border border-border-medium rounded-md text-sm bg-surface text-text-primary placeholder:text-text-tertiary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:border-primary-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4 space-x-reverse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-6 w-6 text-text-secondary\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute -top-1 -right-1 h-4 w-4 bg-accent-red-500 text-text-inverse text-xs rounded-full flex items-center justify-center\",\n                                    children: \"3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:block text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-text-primary\",\n                                            children: \"المدير\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-text-tertiary\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-text-inverse font-medium text-sm\",\n                                        children: \"أ\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/MainLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/MainLayout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MainLayout: () => (/* binding */ MainLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/layout/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ MainLayout auto */ \n/**\n * Main Layout Component - متبرمج ERP System\n * Combines header, sidebar, and main content area\n */ \n\n\nfunction MainLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                isOpen: sidebarOpen,\n                onClose: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/MainLayout.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:mr-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_2__.Header, {\n                        onMenuToggle: ()=>setSidebarOpen(true)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/MainLayout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"bg-background\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/MainLayout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/MainLayout.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/MainLayout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/MainLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/FolderIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n/**\n * Sidebar Navigation Component - متبرمج ERP System\n * Arabic RTL sidebar with navigation menu\n */ \n\n\n\n\nconst navigation = [\n    {\n        name: 'الرئيسية',\n        href: '/',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: 'لوحة التحكم',\n        href: '/dashboard',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'المشاريع',\n        href: '/projects',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'العملاء',\n        href: '/clients',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: 'المهام',\n        href: '/tasks',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: 'فريق المبيعات',\n        href: '/sales-team',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: 'مشتري الإعلانات',\n        href: '/media-buyers',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'المطورين',\n        href: '/developers',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: 'المصممين',\n        href: '/designers',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        name: 'مطوري ووردبريس',\n        href: '/wordpress-developers',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: 'التجديدات السنوية',\n        href: '/annual-renewals',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        name: 'الباقات',\n        href: '/packages',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'المصروفات',\n        href: '/expenses',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    },\n    {\n        name: 'التقارير',\n        href: '/reports',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'إعدادات النظام',\n        href: '/settings',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    }\n];\nfunction Sidebar({ isOpen, onClose }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-25 lg:hidden\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('fixed inset-y-0 right-0 z-50 w-64 bg-surface shadow-lg transform transition-transform duration-300 ease-in-out', isOpen ? 'translate-x-0' : 'translate-x-full lg:translate-x-0'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-16 px-4 bg-primary-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-surface rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary-600 font-bold text-lg\",\n                                            children: \"م\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-text-inverse font-bold text-xl\",\n                                        children: \"متبرمج\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-4 py-6 space-y-1 overflow-y-auto\",\n                            children: navigation.map((item)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors', isActive ? 'bg-primary-100 text-primary-700' : 'text-text-secondary hover:bg-secondary-100 hover:text-text-primary'),\n                                    onClick: ()=>onClose(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('ml-3 h-5 w-5 flex-shrink-0', isActive ? 'text-primary-500' : 'text-text-tertiary group-hover:text-text-secondary')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 19\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 p-4 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 font-medium text-sm\",\n                                            children: \"أ\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"المدير\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"<EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/projects/ProjectForm.tsx":
/*!*************************************************!*\
  !*** ./src/components/projects/ProjectForm.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectForm: () => (/* binding */ ProjectForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _types_project__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/types/project */ \"(ssr)/./src/types/project.ts\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Input */ \"(ssr)/./src/components/ui/Input.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProjectForm auto */ \n/**\n * Project Form Component - متبرمج ERP System\n * Features: React Hook Form + Zod validation, Arabic labels, RTL layout\n */ \n\n\n\n\n\n\n// Zod validation schema\nconst projectSchema = zod__WEBPACK_IMPORTED_MODULE_3__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().min(1, 'اسم المشروع مطلوب').max(200, 'اسم المشروع طويل جداً'),\n    description: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().optional(),\n    start_date: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().min(1, 'تاريخ البداية مطلوب'),\n    expected_end_date: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().min(1, 'تاريخ الانتهاء المتوقع مطلوب'),\n    actual_end_date: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().optional(),\n    status: zod__WEBPACK_IMPORTED_MODULE_3__.z.nativeEnum(_types_project__WEBPACK_IMPORTED_MODULE_4__.ProjectStatus),\n    priority: zod__WEBPACK_IMPORTED_MODULE_3__.z.nativeEnum(_types_project__WEBPACK_IMPORTED_MODULE_4__.ProjectPriority),\n    progress_percentage: zod__WEBPACK_IMPORTED_MODULE_3__.z.number().min(0, 'النسبة لا يمكن أن تكون أقل من 0').max(100, 'النسبة لا يمكن أن تكون أكثر من 100'),\n    estimated_hours: zod__WEBPACK_IMPORTED_MODULE_3__.z.number().min(0, 'الساعات المقدرة لا يمكن أن تكون سالبة'),\n    actual_hours: zod__WEBPACK_IMPORTED_MODULE_3__.z.number().min(0, 'الساعات الفعلية لا يمكن أن تكون سالبة'),\n    implementation_phases: zod__WEBPACK_IMPORTED_MODULE_3__.z.array(zod__WEBPACK_IMPORTED_MODULE_3__.z.string()).default([]),\n    technologies_used: zod__WEBPACK_IMPORTED_MODULE_3__.z.array(zod__WEBPACK_IMPORTED_MODULE_3__.z.string()).default([]),\n    domain_link: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().url('رابط غير صحيح').optional().or(zod__WEBPACK_IMPORTED_MODULE_3__.z.literal('')),\n    domain_email: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().email('بريد إلكتروني غير صحيح').optional().or(zod__WEBPACK_IMPORTED_MODULE_3__.z.literal('')),\n    domain_password: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().optional(),\n    server_link: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().url('رابط غير صحيح').optional().or(zod__WEBPACK_IMPORTED_MODULE_3__.z.literal('')),\n    server_email: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().email('بريد إلكتروني غير صحيح').optional().or(zod__WEBPACK_IMPORTED_MODULE_3__.z.literal('')),\n    server_password: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().optional(),\n    whatsapp_group_link: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().url('رابط غير صحيح').optional().or(zod__WEBPACK_IMPORTED_MODULE_3__.z.literal('')),\n    slack_channel: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().optional(),\n    budget: zod__WEBPACK_IMPORTED_MODULE_3__.z.number().min(0, 'الميزانية لا يمكن أن تكون سالبة'),\n    actual_cost: zod__WEBPACK_IMPORTED_MODULE_3__.z.number().min(0, 'التكلفة الفعلية لا يمكن أن تكون سالبة')\n});\nfunction ProjectForm({ project, onSubmit, onCancel, loading = false }) {\n    const { register, handleSubmit, control, watch, setValue, formState: { errors, isValid } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(projectSchema),\n        defaultValues: project ? {\n            name: project.name,\n            description: project.description || '',\n            start_date: project.start_date,\n            expected_end_date: project.expected_end_date,\n            actual_end_date: project.actual_end_date || '',\n            status: project.status,\n            priority: project.priority,\n            progress_percentage: project.progress_percentage,\n            estimated_hours: project.estimated_hours,\n            actual_hours: project.actual_hours,\n            implementation_phases: project.implementation_phases || [],\n            technologies_used: project.technologies_used || [],\n            domain_link: project.domain_link || '',\n            domain_email: project.domain_email || '',\n            domain_password: project.domain_password || '',\n            server_link: project.server_link || '',\n            server_email: project.server_email || '',\n            server_password: project.server_password || '',\n            whatsapp_group_link: project.whatsapp_group_link || '',\n            slack_channel: project.slack_channel || '',\n            budget: project.budget,\n            actual_cost: project.actual_cost\n        } : {\n            name: '',\n            description: '',\n            start_date: '',\n            expected_end_date: '',\n            actual_end_date: '',\n            status: _types_project__WEBPACK_IMPORTED_MODULE_4__.ProjectStatus.PLANNING,\n            priority: _types_project__WEBPACK_IMPORTED_MODULE_4__.ProjectPriority.MEDIUM,\n            progress_percentage: 0,\n            estimated_hours: 0,\n            actual_hours: 0,\n            implementation_phases: [],\n            technologies_used: [],\n            domain_link: '',\n            domain_email: '',\n            domain_password: '',\n            server_link: '',\n            server_email: '',\n            server_password: '',\n            whatsapp_group_link: '',\n            slack_channel: '',\n            budget: 0,\n            actual_cost: 0\n        }\n    });\n    const handleFormSubmit = (data)=>{\n        onSubmit(data);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit(handleFormSubmit),\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"المعلومات الأساسية\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                label: \"اسم المشروع *\",\n                                ...register('name'),\n                                error: errors.name?.message,\n                                placeholder: \"أدخل اسم المشروع\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"وصف المشروع\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ...register('description'),\n                                        rows: 3,\n                                        className: \"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500\",\n                                        placeholder: \"أدخل وصف تفصيلي للمشروع\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-red-600\",\n                                        children: errors.description.message\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"التواريخ والجدولة\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                label: \"تاريخ البداية *\",\n                                type: \"date\",\n                                ...register('start_date'),\n                                error: errors.start_date?.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                label: \"تاريخ الانتهاء المتوقع *\",\n                                type: \"date\",\n                                ...register('expected_end_date'),\n                                error: errors.expected_end_date?.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                label: \"تاريخ الانتهاء الفعلي\",\n                                type: \"date\",\n                                ...register('actual_end_date'),\n                                error: errors.actual_end_date?.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"إدارة المشروع\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"حالة المشروع *\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_7__.Controller, {\n                                        name: \"status\",\n                                        control: control,\n                                        render: ({ field })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                ...field,\n                                                className: \"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500\",\n                                                children: _types_project__WEBPACK_IMPORTED_MODULE_4__.ProjectStatusOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: option.value,\n                                                        children: option.label\n                                                    }, option.value, false, {\n                                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 21\n                                                    }, void 0))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-red-600\",\n                                        children: errors.status.message\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"الأولوية *\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_7__.Controller, {\n                                        name: \"priority\",\n                                        control: control,\n                                        render: ({ field })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                ...field,\n                                                className: \"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500\",\n                                                children: _types_project__WEBPACK_IMPORTED_MODULE_4__.ProjectPriorityOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: option.value,\n                                                        children: option.label\n                                                    }, option.value, false, {\n                                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 21\n                                                    }, void 0))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.priority && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-red-600\",\n                                        children: errors.priority.message\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                label: \"نسبة الإنجاز (%)\",\n                                type: \"number\",\n                                min: \"0\",\n                                max: \"100\",\n                                ...register('progress_percentage', {\n                                    valueAsNumber: true\n                                }),\n                                error: errors.progress_percentage?.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"الموارد والساعات\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                label: \"الساعات المقدرة\",\n                                type: \"number\",\n                                min: \"0\",\n                                ...register('estimated_hours', {\n                                    valueAsNumber: true\n                                }),\n                                error: errors.estimated_hours?.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                label: \"الساعات الفعلية\",\n                                type: \"number\",\n                                min: \"0\",\n                                ...register('actual_hours', {\n                                    valueAsNumber: true\n                                }),\n                                error: errors.actual_hours?.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"المالية\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                label: \"الميزانية (ج.م)\",\n                                type: \"number\",\n                                min: \"0\",\n                                step: \"0.01\",\n                                ...register('budget', {\n                                    valueAsNumber: true\n                                }),\n                                error: errors.budget?.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                label: \"التكلفة الفعلية (ج.م)\",\n                                type: \"number\",\n                                min: \"0\",\n                                step: \"0.01\",\n                                ...register('actual_cost', {\n                                    valueAsNumber: true\n                                }),\n                                error: errors.actual_cost?.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end space-x-3 space-x-reverse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        type: \"button\",\n                        variant: \"outline\",\n                        onClick: onCancel,\n                        disabled: loading,\n                        children: \"إلغاء\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        type: \"submit\",\n                        loading: loading,\n                        disabled: !isValid,\n                        children: project ? 'تحديث المشروع' : 'إنشاء المشروع'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectForm.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm9qZWN0cy9Qcm9qZWN0Rm9ybS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRUE7OztDQUdDLEdBRXdCO0FBQzRCO0FBQ0E7QUFDOUI7QUFDaUg7QUFDekY7QUFDRjtBQUc3Qyx3QkFBd0I7QUFDeEIsTUFBTVcsZ0JBQWdCUCx5Q0FBUSxDQUFDO0lBQzdCUyxNQUFNVCx5Q0FBUSxHQUFHVyxHQUFHLENBQUMsR0FBRyxxQkFBcUJDLEdBQUcsQ0FBQyxLQUFLO0lBQ3REQyxhQUFhYix5Q0FBUSxHQUFHYyxRQUFRO0lBQ2hDQyxZQUFZZix5Q0FBUSxHQUFHVyxHQUFHLENBQUMsR0FBRztJQUM5QkssbUJBQW1CaEIseUNBQVEsR0FBR1csR0FBRyxDQUFDLEdBQUc7SUFDckNNLGlCQUFpQmpCLHlDQUFRLEdBQUdjLFFBQVE7SUFDcENJLFFBQVFsQiw2Q0FBWSxDQUFDQyx5REFBYUE7SUFDbENtQixVQUFVcEIsNkNBQVksQ0FBQ0UsMkRBQWVBO0lBQ3RDbUIscUJBQXFCckIseUNBQVEsR0FBR1csR0FBRyxDQUFDLEdBQUcsbUNBQW1DQyxHQUFHLENBQUMsS0FBSztJQUNuRlcsaUJBQWlCdkIseUNBQVEsR0FBR1csR0FBRyxDQUFDLEdBQUc7SUFDbkNhLGNBQWN4Qix5Q0FBUSxHQUFHVyxHQUFHLENBQUMsR0FBRztJQUNoQ2MsdUJBQXVCekIsd0NBQU8sQ0FBQ0EseUNBQVEsSUFBSTJCLE9BQU8sQ0FBQyxFQUFFO0lBQ3JEQyxtQkFBbUI1Qix3Q0FBTyxDQUFDQSx5Q0FBUSxJQUFJMkIsT0FBTyxDQUFDLEVBQUU7SUFDakRFLGFBQWE3Qix5Q0FBUSxHQUFHOEIsR0FBRyxDQUFDLGlCQUFpQmhCLFFBQVEsR0FBR2lCLEVBQUUsQ0FBQy9CLDBDQUFTLENBQUM7SUFDckVpQyxjQUFjakMseUNBQVEsR0FBR2tDLEtBQUssQ0FBQywwQkFBMEJwQixRQUFRLEdBQUdpQixFQUFFLENBQUMvQiwwQ0FBUyxDQUFDO0lBQ2pGbUMsaUJBQWlCbkMseUNBQVEsR0FBR2MsUUFBUTtJQUNwQ3NCLGFBQWFwQyx5Q0FBUSxHQUFHOEIsR0FBRyxDQUFDLGlCQUFpQmhCLFFBQVEsR0FBR2lCLEVBQUUsQ0FBQy9CLDBDQUFTLENBQUM7SUFDckVxQyxjQUFjckMseUNBQVEsR0FBR2tDLEtBQUssQ0FBQywwQkFBMEJwQixRQUFRLEdBQUdpQixFQUFFLENBQUMvQiwwQ0FBUyxDQUFDO0lBQ2pGc0MsaUJBQWlCdEMseUNBQVEsR0FBR2MsUUFBUTtJQUNwQ3lCLHFCQUFxQnZDLHlDQUFRLEdBQUc4QixHQUFHLENBQUMsaUJBQWlCaEIsUUFBUSxHQUFHaUIsRUFBRSxDQUFDL0IsMENBQVMsQ0FBQztJQUM3RXdDLGVBQWV4Qyx5Q0FBUSxHQUFHYyxRQUFRO0lBQ2xDMkIsUUFBUXpDLHlDQUFRLEdBQUdXLEdBQUcsQ0FBQyxHQUFHO0lBQzFCK0IsYUFBYTFDLHlDQUFRLEdBQUdXLEdBQUcsQ0FBQyxHQUFHO0FBQ2pDO0FBV08sU0FBU2dDLFlBQVksRUFBRUMsT0FBTyxFQUFFQyxRQUFRLEVBQUVDLFFBQVEsRUFBRUMsVUFBVSxLQUFLLEVBQW9CO0lBQzVGLE1BQU0sRUFDSkMsUUFBUSxFQUNSQyxZQUFZLEVBQ1pDLE9BQU8sRUFDUEMsS0FBSyxFQUNMQyxRQUFRLEVBQ1JDLFdBQVcsRUFBRUMsTUFBTSxFQUFFQyxPQUFPLEVBQUUsRUFDL0IsR0FBRzFELHdEQUFPQSxDQUFvQjtRQUM3QjJELFVBQVV6RCxvRUFBV0EsQ0FBQ1E7UUFDdEJrRCxlQUFlYixVQUFVO1lBQ3ZCbkMsTUFBTW1DLFFBQVFuQyxJQUFJO1lBQ2xCSSxhQUFhK0IsUUFBUS9CLFdBQVcsSUFBSTtZQUNwQ0UsWUFBWTZCLFFBQVE3QixVQUFVO1lBQzlCQyxtQkFBbUI0QixRQUFRNUIsaUJBQWlCO1lBQzVDQyxpQkFBaUIyQixRQUFRM0IsZUFBZSxJQUFJO1lBQzVDQyxRQUFRMEIsUUFBUTFCLE1BQU07WUFDdEJFLFVBQVV3QixRQUFReEIsUUFBUTtZQUMxQkMscUJBQXFCdUIsUUFBUXZCLG1CQUFtQjtZQUNoREUsaUJBQWlCcUIsUUFBUXJCLGVBQWU7WUFDeENDLGNBQWNvQixRQUFRcEIsWUFBWTtZQUNsQ0MsdUJBQXVCbUIsUUFBUW5CLHFCQUFxQixJQUFJLEVBQUU7WUFDMURHLG1CQUFtQmdCLFFBQVFoQixpQkFBaUIsSUFBSSxFQUFFO1lBQ2xEQyxhQUFhZSxRQUFRZixXQUFXLElBQUk7WUFDcENJLGNBQWNXLFFBQVFYLFlBQVksSUFBSTtZQUN0Q0UsaUJBQWlCUyxRQUFRVCxlQUFlLElBQUk7WUFDNUNDLGFBQWFRLFFBQVFSLFdBQVcsSUFBSTtZQUNwQ0MsY0FBY08sUUFBUVAsWUFBWSxJQUFJO1lBQ3RDQyxpQkFBaUJNLFFBQVFOLGVBQWUsSUFBSTtZQUM1Q0MscUJBQXFCSyxRQUFRTCxtQkFBbUIsSUFBSTtZQUNwREMsZUFBZUksUUFBUUosYUFBYSxJQUFJO1lBQ3hDQyxRQUFRRyxRQUFRSCxNQUFNO1lBQ3RCQyxhQUFhRSxRQUFRRixXQUFXO1FBQ2xDLElBQUk7WUFDRmpDLE1BQU07WUFDTkksYUFBYTtZQUNiRSxZQUFZO1lBQ1pDLG1CQUFtQjtZQUNuQkMsaUJBQWlCO1lBQ2pCQyxRQUFRakIseURBQWFBLENBQUN5RCxRQUFRO1lBQzlCdEMsVUFBVWxCLDJEQUFlQSxDQUFDeUQsTUFBTTtZQUNoQ3RDLHFCQUFxQjtZQUNyQkUsaUJBQWlCO1lBQ2pCQyxjQUFjO1lBQ2RDLHVCQUF1QixFQUFFO1lBQ3pCRyxtQkFBbUIsRUFBRTtZQUNyQkMsYUFBYTtZQUNiSSxjQUFjO1lBQ2RFLGlCQUFpQjtZQUNqQkMsYUFBYTtZQUNiQyxjQUFjO1lBQ2RDLGlCQUFpQjtZQUNqQkMscUJBQXFCO1lBQ3JCQyxlQUFlO1lBQ2ZDLFFBQVE7WUFDUkMsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNa0IsbUJBQW1CLENBQUNDO1FBQ3hCaEIsU0FBU2dCO0lBQ1g7SUFFQSxxQkFDRSw4REFBQ0M7UUFBS2pCLFVBQVVJLGFBQWFXO1FBQW1CRyxXQUFVOzswQkFFeEQsOERBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0U7d0JBQUdGLFdBQVU7a0NBQXlDOzs7Ozs7a0NBQ3ZELDhEQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUN6RCx1REFBS0E7Z0NBQ0o0RCxPQUFNO2dDQUNMLEdBQUdsQixTQUFTLE9BQU87Z0NBQ3BCbUIsT0FBT2IsT0FBTzdDLElBQUksRUFBRTJEO2dDQUNwQkMsYUFBWTs7Ozs7OzBDQUdkLDhEQUFDTDtnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNHO3dDQUFNSCxXQUFVO2tEQUErQzs7Ozs7O2tEQUdoRSw4REFBQ087d0NBQ0UsR0FBR3RCLFNBQVMsY0FBYzt3Q0FDM0J1QixNQUFNO3dDQUNOUixXQUFVO3dDQUNWTSxhQUFZOzs7Ozs7b0NBRWJmLE9BQU96QyxXQUFXLGtCQUNqQiw4REFBQzJEO3dDQUFFVCxXQUFVO2tEQUE2QlQsT0FBT3pDLFdBQVcsQ0FBQ3VELE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPNUUsOERBQUNKO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0U7d0JBQUdGLFdBQVU7a0NBQXlDOzs7Ozs7a0NBQ3ZELDhEQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUN6RCx1REFBS0E7Z0NBQ0o0RCxPQUFNO2dDQUNOTyxNQUFLO2dDQUNKLEdBQUd6QixTQUFTLGFBQWE7Z0NBQzFCbUIsT0FBT2IsT0FBT3ZDLFVBQVUsRUFBRXFEOzs7Ozs7MENBRzVCLDhEQUFDOUQsdURBQUtBO2dDQUNKNEQsT0FBTTtnQ0FDTk8sTUFBSztnQ0FDSixHQUFHekIsU0FBUyxvQkFBb0I7Z0NBQ2pDbUIsT0FBT2IsT0FBT3RDLGlCQUFpQixFQUFFb0Q7Ozs7OzswQ0FHbkMsOERBQUM5RCx1REFBS0E7Z0NBQ0o0RCxPQUFNO2dDQUNOTyxNQUFLO2dDQUNKLEdBQUd6QixTQUFTLGtCQUFrQjtnQ0FDL0JtQixPQUFPYixPQUFPckMsZUFBZSxFQUFFbUQ7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNckMsOERBQUNKO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0U7d0JBQUdGLFdBQVU7a0NBQXlDOzs7Ozs7a0NBQ3ZELDhEQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNDOztrREFDQyw4REFBQ0U7d0NBQU1ILFdBQVU7a0RBQStDOzs7Ozs7a0RBR2hFLDhEQUFDakUsdURBQVVBO3dDQUNUVyxNQUFLO3dDQUNMeUMsU0FBU0E7d0NBQ1R3QixRQUFRLENBQUMsRUFBRUMsS0FBSyxFQUFFLGlCQUNoQiw4REFBQ0M7Z0RBQ0UsR0FBR0QsS0FBSztnREFDVFosV0FBVTswREFFVDVELGdFQUFvQkEsQ0FBQzBFLEdBQUcsQ0FBQyxDQUFDQyx1QkFDekIsOERBQUNBO3dEQUEwQkMsT0FBT0QsT0FBT0MsS0FBSztrRUFDM0NELE9BQU9aLEtBQUs7dURBREZZLE9BQU9DLEtBQUs7Ozs7Ozs7Ozs7Ozs7OztvQ0FPaEN6QixPQUFPcEMsTUFBTSxrQkFDWiw4REFBQ3NEO3dDQUFFVCxXQUFVO2tEQUE2QlQsT0FBT3BDLE1BQU0sQ0FBQ2tELE9BQU87Ozs7Ozs7Ozs7OzswQ0FJbkUsOERBQUNKOztrREFDQyw4REFBQ0U7d0NBQU1ILFdBQVU7a0RBQStDOzs7Ozs7a0RBR2hFLDhEQUFDakUsdURBQVVBO3dDQUNUVyxNQUFLO3dDQUNMeUMsU0FBU0E7d0NBQ1R3QixRQUFRLENBQUMsRUFBRUMsS0FBSyxFQUFFLGlCQUNoQiw4REFBQ0M7Z0RBQ0UsR0FBR0QsS0FBSztnREFDVFosV0FBVTswREFFVDNELGtFQUFzQkEsQ0FBQ3lFLEdBQUcsQ0FBQyxDQUFDQyx1QkFDM0IsOERBQUNBO3dEQUEwQkMsT0FBT0QsT0FBT0MsS0FBSztrRUFDM0NELE9BQU9aLEtBQUs7dURBREZZLE9BQU9DLEtBQUs7Ozs7Ozs7Ozs7Ozs7OztvQ0FPaEN6QixPQUFPbEMsUUFBUSxrQkFDZCw4REFBQ29EO3dDQUFFVCxXQUFVO2tEQUE2QlQsT0FBT2xDLFFBQVEsQ0FBQ2dELE9BQU87Ozs7Ozs7Ozs7OzswQ0FJckUsOERBQUM5RCx1REFBS0E7Z0NBQ0o0RCxPQUFNO2dDQUNOTyxNQUFLO2dDQUNMOUQsS0FBSTtnQ0FDSkMsS0FBSTtnQ0FDSCxHQUFHb0MsU0FBUyx1QkFBdUI7b0NBQUVnQyxlQUFlO2dDQUFLLEVBQUU7Z0NBQzVEYixPQUFPYixPQUFPakMsbUJBQW1CLEVBQUUrQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU16Qyw4REFBQ0o7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDRTt3QkFBR0YsV0FBVTtrQ0FBeUM7Ozs7OztrQ0FDdkQsOERBQUNDO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ3pELHVEQUFLQTtnQ0FDSjRELE9BQU07Z0NBQ05PLE1BQUs7Z0NBQ0w5RCxLQUFJO2dDQUNILEdBQUdxQyxTQUFTLG1CQUFtQjtvQ0FBRWdDLGVBQWU7Z0NBQUssRUFBRTtnQ0FDeERiLE9BQU9iLE9BQU8vQixlQUFlLEVBQUU2Qzs7Ozs7OzBDQUdqQyw4REFBQzlELHVEQUFLQTtnQ0FDSjRELE9BQU07Z0NBQ05PLE1BQUs7Z0NBQ0w5RCxLQUFJO2dDQUNILEdBQUdxQyxTQUFTLGdCQUFnQjtvQ0FBRWdDLGVBQWU7Z0NBQUssRUFBRTtnQ0FDckRiLE9BQU9iLE9BQU85QixZQUFZLEVBQUU0Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU1sQyw4REFBQ0o7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDRTt3QkFBR0YsV0FBVTtrQ0FBeUM7Ozs7OztrQ0FDdkQsOERBQUNDO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ3pELHVEQUFLQTtnQ0FDSjRELE9BQU07Z0NBQ05PLE1BQUs7Z0NBQ0w5RCxLQUFJO2dDQUNKc0UsTUFBSztnQ0FDSixHQUFHakMsU0FBUyxVQUFVO29DQUFFZ0MsZUFBZTtnQ0FBSyxFQUFFO2dDQUMvQ2IsT0FBT2IsT0FBT2IsTUFBTSxFQUFFMkI7Ozs7OzswQ0FHeEIsOERBQUM5RCx1REFBS0E7Z0NBQ0o0RCxPQUFNO2dDQUNOTyxNQUFLO2dDQUNMOUQsS0FBSTtnQ0FDSnNFLE1BQUs7Z0NBQ0osR0FBR2pDLFNBQVMsZUFBZTtvQ0FBRWdDLGVBQWU7Z0NBQUssRUFBRTtnQ0FDcERiLE9BQU9iLE9BQU9aLFdBQVcsRUFBRTBCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTWpDLDhEQUFDSjtnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUMxRCx5REFBTUE7d0JBQ0xvRSxNQUFLO3dCQUNMUyxTQUFRO3dCQUNSQyxTQUFTckM7d0JBQ1RzQyxVQUFVckM7a0NBQ1g7Ozs7OztrQ0FHRCw4REFBQzFDLHlEQUFNQTt3QkFDTG9FLE1BQUs7d0JBQ0wxQixTQUFTQTt3QkFDVHFDLFVBQVUsQ0FBQzdCO2tDQUVWWCxVQUFVLGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS3ZDIiwic291cmNlcyI6WyIvVXNlcnMvbXVoYW1tYWR5b3Vzc2VmL1NpdGVzL25leHRqc19lcnAvc3JjL2NvbXBvbmVudHMvcHJvamVjdHMvUHJvamVjdEZvcm0udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG4vKipcbiAqIFByb2plY3QgRm9ybSBDb21wb25lbnQgLSDZhdiq2KjYsdmF2KwgRVJQIFN5c3RlbVxuICogRmVhdHVyZXM6IFJlYWN0IEhvb2sgRm9ybSArIFpvZCB2YWxpZGF0aW9uLCBBcmFiaWMgbGFiZWxzLCBSVEwgbGF5b3V0XG4gKi9cblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgdXNlRm9ybSwgQ29udHJvbGxlciB9IGZyb20gJ3JlYWN0LWhvb2stZm9ybSdcbmltcG9ydCB7IHpvZFJlc29sdmVyIH0gZnJvbSAnQGhvb2tmb3JtL3Jlc29sdmVycy96b2QnXG5pbXBvcnQgeyB6IH0gZnJvbSAnem9kJ1xuaW1wb3J0IHsgUHJvamVjdCwgUHJvamVjdEZvcm1EYXRhLCBQcm9qZWN0U3RhdHVzLCBQcm9qZWN0UHJpb3JpdHksIFByb2plY3RTdGF0dXNPcHRpb25zLCBQcm9qZWN0UHJpb3JpdHlPcHRpb25zIH0gZnJvbSAnQC90eXBlcy9wcm9qZWN0J1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0J1dHRvbidcbmltcG9ydCB7IElucHV0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0lucHV0J1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscydcblxuLy8gWm9kIHZhbGlkYXRpb24gc2NoZW1hXG5jb25zdCBwcm9qZWN0U2NoZW1hID0gei5vYmplY3Qoe1xuICBuYW1lOiB6LnN0cmluZygpLm1pbigxLCAn2KfYs9mFINin2YTZhdi02LHZiNi5INmF2LfZhNmI2KgnKS5tYXgoMjAwLCAn2KfYs9mFINin2YTZhdi02LHZiNi5INi32YjZitmEINis2K/Yp9mLJyksXG4gIGRlc2NyaXB0aW9uOiB6LnN0cmluZygpLm9wdGlvbmFsKCksXG4gIHN0YXJ0X2RhdGU6IHouc3RyaW5nKCkubWluKDEsICfYqtin2LHZitiuINin2YTYqNiv2KfZitipINmF2LfZhNmI2KgnKSxcbiAgZXhwZWN0ZWRfZW5kX2RhdGU6IHouc3RyaW5nKCkubWluKDEsICfYqtin2LHZitiuINin2YTYp9mG2KrZh9in2KEg2KfZhNmF2KrZiNmC2Lkg2YXYt9mE2YjYqCcpLFxuICBhY3R1YWxfZW5kX2RhdGU6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgc3RhdHVzOiB6Lm5hdGl2ZUVudW0oUHJvamVjdFN0YXR1cyksXG4gIHByaW9yaXR5OiB6Lm5hdGl2ZUVudW0oUHJvamVjdFByaW9yaXR5KSxcbiAgcHJvZ3Jlc3NfcGVyY2VudGFnZTogei5udW1iZXIoKS5taW4oMCwgJ9in2YTZhtiz2KjYqSDZhNinINmK2YXZg9mGINij2YYg2KrZg9mI2YYg2KPZgtmEINmF2YYgMCcpLm1heCgxMDAsICfYp9mE2YbYs9io2Kkg2YTYpyDZitmF2YPZhiDYo9mGINiq2YPZiNmGINij2YPYq9ixINmF2YYgMTAwJyksXG4gIGVzdGltYXRlZF9ob3Vyczogei5udW1iZXIoKS5taW4oMCwgJ9in2YTYs9in2LnYp9iqINin2YTZhdmC2K/YsdipINmE2Kcg2YrZhdmD2YYg2KPZhiDYqtmD2YjZhiDYs9in2YTYqNipJyksXG4gIGFjdHVhbF9ob3Vyczogei5udW1iZXIoKS5taW4oMCwgJ9in2YTYs9in2LnYp9iqINin2YTZgdi52YTZitipINmE2Kcg2YrZhdmD2YYg2KPZhiDYqtmD2YjZhiDYs9in2YTYqNipJyksXG4gIGltcGxlbWVudGF0aW9uX3BoYXNlczogei5hcnJheSh6LnN0cmluZygpKS5kZWZhdWx0KFtdKSxcbiAgdGVjaG5vbG9naWVzX3VzZWQ6IHouYXJyYXkoei5zdHJpbmcoKSkuZGVmYXVsdChbXSksXG4gIGRvbWFpbl9saW5rOiB6LnN0cmluZygpLnVybCgn2LHYp9io2Lcg2LrZitixINi12K3ZititJykub3B0aW9uYWwoKS5vcih6LmxpdGVyYWwoJycpKSxcbiAgZG9tYWluX2VtYWlsOiB6LnN0cmluZygpLmVtYWlsKCfYqNix2YrYryDYpdmE2YPYqtix2YjZhtmKINi62YrYsSDYtdit2YrYrScpLm9wdGlvbmFsKCkub3Ioei5saXRlcmFsKCcnKSksXG4gIGRvbWFpbl9wYXNzd29yZDogei5zdHJpbmcoKS5vcHRpb25hbCgpLFxuICBzZXJ2ZXJfbGluazogei5zdHJpbmcoKS51cmwoJ9ix2KfYqNi3INi62YrYsSDYtdit2YrYrScpLm9wdGlvbmFsKCkub3Ioei5saXRlcmFsKCcnKSksXG4gIHNlcnZlcl9lbWFpbDogei5zdHJpbmcoKS5lbWFpbCgn2KjYsdmK2K8g2KXZhNmD2KrYsdmI2YbZiiDYutmK2LEg2LXYrdmK2K0nKS5vcHRpb25hbCgpLm9yKHoubGl0ZXJhbCgnJykpLFxuICBzZXJ2ZXJfcGFzc3dvcmQ6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgd2hhdHNhcHBfZ3JvdXBfbGluazogei5zdHJpbmcoKS51cmwoJ9ix2KfYqNi3INi62YrYsSDYtdit2YrYrScpLm9wdGlvbmFsKCkub3Ioei5saXRlcmFsKCcnKSksXG4gIHNsYWNrX2NoYW5uZWw6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgYnVkZ2V0OiB6Lm51bWJlcigpLm1pbigwLCAn2KfZhNmF2YrYstin2YbZitipINmE2Kcg2YrZhdmD2YYg2KPZhiDYqtmD2YjZhiDYs9in2YTYqNipJyksXG4gIGFjdHVhbF9jb3N0OiB6Lm51bWJlcigpLm1pbigwLCAn2KfZhNiq2YPZhNmB2Kkg2KfZhNmB2LnZhNmK2Kkg2YTYpyDZitmF2YPZhiDYo9mGINiq2YPZiNmGINiz2KfZhNio2KknKSxcbn0pXG5cbnR5cGUgUHJvamVjdEZvcm1WYWx1ZXMgPSB6LmluZmVyPHR5cGVvZiBwcm9qZWN0U2NoZW1hPlxuXG5pbnRlcmZhY2UgUHJvamVjdEZvcm1Qcm9wcyB7XG4gIHByb2plY3Q/OiBQcm9qZWN0XG4gIG9uU3VibWl0OiAoZGF0YTogUHJvamVjdEZvcm1EYXRhKSA9PiB2b2lkXG4gIG9uQ2FuY2VsOiAoKSA9PiB2b2lkXG4gIGxvYWRpbmc/OiBib29sZWFuXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBQcm9qZWN0Rm9ybSh7IHByb2plY3QsIG9uU3VibWl0LCBvbkNhbmNlbCwgbG9hZGluZyA9IGZhbHNlIH06IFByb2plY3RGb3JtUHJvcHMpIHtcbiAgY29uc3Qge1xuICAgIHJlZ2lzdGVyLFxuICAgIGhhbmRsZVN1Ym1pdCxcbiAgICBjb250cm9sLFxuICAgIHdhdGNoLFxuICAgIHNldFZhbHVlLFxuICAgIGZvcm1TdGF0ZTogeyBlcnJvcnMsIGlzVmFsaWQgfSxcbiAgfSA9IHVzZUZvcm08UHJvamVjdEZvcm1WYWx1ZXM+KHtcbiAgICByZXNvbHZlcjogem9kUmVzb2x2ZXIocHJvamVjdFNjaGVtYSksXG4gICAgZGVmYXVsdFZhbHVlczogcHJvamVjdCA/IHtcbiAgICAgIG5hbWU6IHByb2plY3QubmFtZSxcbiAgICAgIGRlc2NyaXB0aW9uOiBwcm9qZWN0LmRlc2NyaXB0aW9uIHx8ICcnLFxuICAgICAgc3RhcnRfZGF0ZTogcHJvamVjdC5zdGFydF9kYXRlLFxuICAgICAgZXhwZWN0ZWRfZW5kX2RhdGU6IHByb2plY3QuZXhwZWN0ZWRfZW5kX2RhdGUsXG4gICAgICBhY3R1YWxfZW5kX2RhdGU6IHByb2plY3QuYWN0dWFsX2VuZF9kYXRlIHx8ICcnLFxuICAgICAgc3RhdHVzOiBwcm9qZWN0LnN0YXR1cyxcbiAgICAgIHByaW9yaXR5OiBwcm9qZWN0LnByaW9yaXR5LFxuICAgICAgcHJvZ3Jlc3NfcGVyY2VudGFnZTogcHJvamVjdC5wcm9ncmVzc19wZXJjZW50YWdlLFxuICAgICAgZXN0aW1hdGVkX2hvdXJzOiBwcm9qZWN0LmVzdGltYXRlZF9ob3VycyxcbiAgICAgIGFjdHVhbF9ob3VyczogcHJvamVjdC5hY3R1YWxfaG91cnMsXG4gICAgICBpbXBsZW1lbnRhdGlvbl9waGFzZXM6IHByb2plY3QuaW1wbGVtZW50YXRpb25fcGhhc2VzIHx8IFtdLFxuICAgICAgdGVjaG5vbG9naWVzX3VzZWQ6IHByb2plY3QudGVjaG5vbG9naWVzX3VzZWQgfHwgW10sXG4gICAgICBkb21haW5fbGluazogcHJvamVjdC5kb21haW5fbGluayB8fCAnJyxcbiAgICAgIGRvbWFpbl9lbWFpbDogcHJvamVjdC5kb21haW5fZW1haWwgfHwgJycsXG4gICAgICBkb21haW5fcGFzc3dvcmQ6IHByb2plY3QuZG9tYWluX3Bhc3N3b3JkIHx8ICcnLFxuICAgICAgc2VydmVyX2xpbms6IHByb2plY3Quc2VydmVyX2xpbmsgfHwgJycsXG4gICAgICBzZXJ2ZXJfZW1haWw6IHByb2plY3Quc2VydmVyX2VtYWlsIHx8ICcnLFxuICAgICAgc2VydmVyX3Bhc3N3b3JkOiBwcm9qZWN0LnNlcnZlcl9wYXNzd29yZCB8fCAnJyxcbiAgICAgIHdoYXRzYXBwX2dyb3VwX2xpbms6IHByb2plY3Qud2hhdHNhcHBfZ3JvdXBfbGluayB8fCAnJyxcbiAgICAgIHNsYWNrX2NoYW5uZWw6IHByb2plY3Quc2xhY2tfY2hhbm5lbCB8fCAnJyxcbiAgICAgIGJ1ZGdldDogcHJvamVjdC5idWRnZXQsXG4gICAgICBhY3R1YWxfY29zdDogcHJvamVjdC5hY3R1YWxfY29zdCxcbiAgICB9IDoge1xuICAgICAgbmFtZTogJycsXG4gICAgICBkZXNjcmlwdGlvbjogJycsXG4gICAgICBzdGFydF9kYXRlOiAnJyxcbiAgICAgIGV4cGVjdGVkX2VuZF9kYXRlOiAnJyxcbiAgICAgIGFjdHVhbF9lbmRfZGF0ZTogJycsXG4gICAgICBzdGF0dXM6IFByb2plY3RTdGF0dXMuUExBTk5JTkcsXG4gICAgICBwcmlvcml0eTogUHJvamVjdFByaW9yaXR5Lk1FRElVTSxcbiAgICAgIHByb2dyZXNzX3BlcmNlbnRhZ2U6IDAsXG4gICAgICBlc3RpbWF0ZWRfaG91cnM6IDAsXG4gICAgICBhY3R1YWxfaG91cnM6IDAsXG4gICAgICBpbXBsZW1lbnRhdGlvbl9waGFzZXM6IFtdLFxuICAgICAgdGVjaG5vbG9naWVzX3VzZWQ6IFtdLFxuICAgICAgZG9tYWluX2xpbms6ICcnLFxuICAgICAgZG9tYWluX2VtYWlsOiAnJyxcbiAgICAgIGRvbWFpbl9wYXNzd29yZDogJycsXG4gICAgICBzZXJ2ZXJfbGluazogJycsXG4gICAgICBzZXJ2ZXJfZW1haWw6ICcnLFxuICAgICAgc2VydmVyX3Bhc3N3b3JkOiAnJyxcbiAgICAgIHdoYXRzYXBwX2dyb3VwX2xpbms6ICcnLFxuICAgICAgc2xhY2tfY2hhbm5lbDogJycsXG4gICAgICBidWRnZXQ6IDAsXG4gICAgICBhY3R1YWxfY29zdDogMCxcbiAgICB9LFxuICB9KVxuXG4gIGNvbnN0IGhhbmRsZUZvcm1TdWJtaXQgPSAoZGF0YTogUHJvamVjdEZvcm1WYWx1ZXMpID0+IHtcbiAgICBvblN1Ym1pdChkYXRhIGFzIFByb2plY3RGb3JtRGF0YSlcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdChoYW5kbGVGb3JtU3VibWl0KX0gY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICB7LyogQmFzaWMgSW5mb3JtYXRpb24gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHAtNiByb3VuZGVkLWxnIHNoYWRvdy1zbSBib3JkZXJcIj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi00XCI+2KfZhNmF2LnZhNmI2YXYp9iqINin2YTYo9iz2KfYs9mK2Kk8L2gzPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgIGxhYmVsPVwi2KfYs9mFINin2YTZhdi02LHZiNi5ICpcIlxuICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCduYW1lJyl9XG4gICAgICAgICAgICBlcnJvcj17ZXJyb3JzLm5hbWU/Lm1lc3NhZ2V9XG4gICAgICAgICAgICBwbGFjZWhvbGRlcj1cItij2K/YrtmEINin2LPZhSDYp9mE2YXYtNix2YjYuVwiXG4gICAgICAgICAgLz5cbiAgICAgICAgICBcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1kOmNvbC1zcGFuLTJcIj5cbiAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICDZiNi12YEg2KfZhNmF2LTYsdmI2LlcbiAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdkZXNjcmlwdGlvbicpfVxuICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHB4LTMgcHktMiB0ZXh0LXNtIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi2KPYr9iu2YQg2YjYtdmBINiq2YHYtdmK2YTZiiDZhNmE2YXYtNix2YjYuVwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAge2Vycm9ycy5kZXNjcmlwdGlvbiAmJiAoXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLmRlc2NyaXB0aW9uLm1lc3NhZ2V9PC9wPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIERhdGVzIGFuZCBUaW1lbGluZSAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcC02IHJvdW5kZWQtbGcgc2hhZG93LXNtIGJvcmRlclwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTRcIj7Yp9mE2KrZiNin2LHZitiuINmI2KfZhNis2K/ZiNmE2Kk8L2gzPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTRcIj5cbiAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgIGxhYmVsPVwi2KrYp9ix2YrYriDYp9mE2KjYr9in2YrYqSAqXCJcbiAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcbiAgICAgICAgICAgIHsuLi5yZWdpc3Rlcignc3RhcnRfZGF0ZScpfVxuICAgICAgICAgICAgZXJyb3I9e2Vycm9ycy5zdGFydF9kYXRlPy5tZXNzYWdlfVxuICAgICAgICAgIC8+XG4gICAgICAgICAgXG4gICAgICAgICAgPElucHV0XG4gICAgICAgICAgICBsYWJlbD1cItiq2KfYsdmK2K4g2KfZhNin2YbYqtmH2KfYoSDYp9mE2YXYqtmI2YLYuSAqXCJcbiAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcbiAgICAgICAgICAgIHsuLi5yZWdpc3RlcignZXhwZWN0ZWRfZW5kX2RhdGUnKX1cbiAgICAgICAgICAgIGVycm9yPXtlcnJvcnMuZXhwZWN0ZWRfZW5kX2RhdGU/Lm1lc3NhZ2V9XG4gICAgICAgICAgLz5cbiAgICAgICAgICBcbiAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgIGxhYmVsPVwi2KrYp9ix2YrYriDYp9mE2KfZhtiq2YfYp9ihINin2YTZgdi52YTZilwiXG4gICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXG4gICAgICAgICAgICB7Li4ucmVnaXN0ZXIoJ2FjdHVhbF9lbmRfZGF0ZScpfVxuICAgICAgICAgICAgZXJyb3I9e2Vycm9ycy5hY3R1YWxfZW5kX2RhdGU/Lm1lc3NhZ2V9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFByb2plY3QgTWFuYWdlbWVudCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcC02IHJvdW5kZWQtbGcgc2hhZG93LXNtIGJvcmRlclwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTRcIj7Ypdiv2KfYsdipINin2YTZhdi02LHZiNi5PC9oMz5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC00XCI+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICDYrdin2YTYqSDYp9mE2YXYtNix2YjYuSAqXG4gICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgPENvbnRyb2xsZXJcbiAgICAgICAgICAgICAgbmFtZT1cInN0YXR1c1wiXG4gICAgICAgICAgICAgIGNvbnRyb2w9e2NvbnRyb2x9XG4gICAgICAgICAgICAgIHJlbmRlcj17KHsgZmllbGQgfSkgPT4gKFxuICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgIHsuLi5maWVsZH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcHgtMyBweS0yIHRleHQtc20gZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7UHJvamVjdFN0YXR1c09wdGlvbnMubWFwKChvcHRpb24pID0+IChcbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e29wdGlvbi52YWx1ZX0gdmFsdWU9e29wdGlvbi52YWx1ZX0+XG4gICAgICAgICAgICAgICAgICAgICAge29wdGlvbi5sYWJlbH1cbiAgICAgICAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICB7ZXJyb3JzLnN0YXR1cyAmJiAoXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj57ZXJyb3JzLnN0YXR1cy5tZXNzYWdlfTwvcD5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICDYp9mE2KPZiNmE2YjZitipICpcbiAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICA8Q29udHJvbGxlclxuICAgICAgICAgICAgICBuYW1lPVwicHJpb3JpdHlcIlxuICAgICAgICAgICAgICBjb250cm9sPXtjb250cm9sfVxuICAgICAgICAgICAgICByZW5kZXI9eyh7IGZpZWxkIH0pID0+IChcbiAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICB7Li4uZmllbGR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHB4LTMgcHktMiB0ZXh0LXNtIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge1Byb2plY3RQcmlvcml0eU9wdGlvbnMubWFwKChvcHRpb24pID0+IChcbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e29wdGlvbi52YWx1ZX0gdmFsdWU9e29wdGlvbi52YWx1ZX0+XG4gICAgICAgICAgICAgICAgICAgICAge29wdGlvbi5sYWJlbH1cbiAgICAgICAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICB7ZXJyb3JzLnByaW9yaXR5ICYmIChcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMucHJpb3JpdHkubWVzc2FnZX08L3A+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgbGFiZWw9XCLZhtiz2KjYqSDYp9mE2KXZhtis2KfYsiAoJSlcIlxuICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgIG1heD1cIjEwMFwiXG4gICAgICAgICAgICB7Li4ucmVnaXN0ZXIoJ3Byb2dyZXNzX3BlcmNlbnRhZ2UnLCB7IHZhbHVlQXNOdW1iZXI6IHRydWUgfSl9XG4gICAgICAgICAgICBlcnJvcj17ZXJyb3JzLnByb2dyZXNzX3BlcmNlbnRhZ2U/Lm1lc3NhZ2V9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFJlc291cmNlcyBhbmQgSG91cnMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHAtNiByb3VuZGVkLWxnIHNoYWRvdy1zbSBib3JkZXJcIj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi00XCI+2KfZhNmF2YjYp9ix2K8g2YjYp9mE2LPYp9i52KfYqjwvaDM+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgbGFiZWw9XCLYp9mE2LPYp9i52KfYqiDYp9mE2YXZgtiv2LHYqVwiXG4gICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgIG1pbj1cIjBcIlxuICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdlc3RpbWF0ZWRfaG91cnMnLCB7IHZhbHVlQXNOdW1iZXI6IHRydWUgfSl9XG4gICAgICAgICAgICBlcnJvcj17ZXJyb3JzLmVzdGltYXRlZF9ob3Vycz8ubWVzc2FnZX1cbiAgICAgICAgICAvPlxuICAgICAgICAgIFxuICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgbGFiZWw9XCLYp9mE2LPYp9i52KfYqiDYp9mE2YHYudmE2YrYqVwiXG4gICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgIG1pbj1cIjBcIlxuICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdhY3R1YWxfaG91cnMnLCB7IHZhbHVlQXNOdW1iZXI6IHRydWUgfSl9XG4gICAgICAgICAgICBlcnJvcj17ZXJyb3JzLmFjdHVhbF9ob3Vycz8ubWVzc2FnZX1cbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRmluYW5jaWFsICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBwLTYgcm91bmRlZC1sZyBzaGFkb3ctc20gYm9yZGVyXCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItNFwiPtin2YTZhdin2YTZitipPC9oMz5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgPElucHV0XG4gICAgICAgICAgICBsYWJlbD1cItin2YTZhdmK2LLYp9mG2YrYqSAo2Kwu2YUpXCJcbiAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgbWluPVwiMFwiXG4gICAgICAgICAgICBzdGVwPVwiMC4wMVwiXG4gICAgICAgICAgICB7Li4ucmVnaXN0ZXIoJ2J1ZGdldCcsIHsgdmFsdWVBc051bWJlcjogdHJ1ZSB9KX1cbiAgICAgICAgICAgIGVycm9yPXtlcnJvcnMuYnVkZ2V0Py5tZXNzYWdlfVxuICAgICAgICAgIC8+XG4gICAgICAgICAgXG4gICAgICAgICAgPElucHV0XG4gICAgICAgICAgICBsYWJlbD1cItin2YTYqtmD2YTZgdipINin2YTZgdi52YTZitipICjYrC7ZhSlcIlxuICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgIHN0ZXA9XCIwLjAxXCJcbiAgICAgICAgICAgIHsuLi5yZWdpc3RlcignYWN0dWFsX2Nvc3QnLCB7IHZhbHVlQXNOdW1iZXI6IHRydWUgfSl9XG4gICAgICAgICAgICBlcnJvcj17ZXJyb3JzLmFjdHVhbF9jb3N0Py5tZXNzYWdlfVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBGb3JtIEFjdGlvbnMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmQgc3BhY2UteC0zIHNwYWNlLXgtcmV2ZXJzZVwiPlxuICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgIG9uQ2xpY2s9e29uQ2FuY2VsfVxuICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICA+XG4gICAgICAgICAg2KXZhNi62KfYoVxuICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPEJ1dHRvblxuICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgIGxvYWRpbmc9e2xvYWRpbmd9XG4gICAgICAgICAgZGlzYWJsZWQ9eyFpc1ZhbGlkfVxuICAgICAgICA+XG4gICAgICAgICAge3Byb2plY3QgPyAn2KrYrdiv2YrYqyDYp9mE2YXYtNix2YjYuScgOiAn2KXZhti02KfYoSDYp9mE2YXYtNix2YjYuSd9XG4gICAgICAgIDwvQnV0dG9uPlxuICAgICAgPC9kaXY+XG4gICAgPC9mb3JtPlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VGb3JtIiwiQ29udHJvbGxlciIsInpvZFJlc29sdmVyIiwieiIsIlByb2plY3RTdGF0dXMiLCJQcm9qZWN0UHJpb3JpdHkiLCJQcm9qZWN0U3RhdHVzT3B0aW9ucyIsIlByb2plY3RQcmlvcml0eU9wdGlvbnMiLCJCdXR0b24iLCJJbnB1dCIsInByb2plY3RTY2hlbWEiLCJvYmplY3QiLCJuYW1lIiwic3RyaW5nIiwibWluIiwibWF4IiwiZGVzY3JpcHRpb24iLCJvcHRpb25hbCIsInN0YXJ0X2RhdGUiLCJleHBlY3RlZF9lbmRfZGF0ZSIsImFjdHVhbF9lbmRfZGF0ZSIsInN0YXR1cyIsIm5hdGl2ZUVudW0iLCJwcmlvcml0eSIsInByb2dyZXNzX3BlcmNlbnRhZ2UiLCJudW1iZXIiLCJlc3RpbWF0ZWRfaG91cnMiLCJhY3R1YWxfaG91cnMiLCJpbXBsZW1lbnRhdGlvbl9waGFzZXMiLCJhcnJheSIsImRlZmF1bHQiLCJ0ZWNobm9sb2dpZXNfdXNlZCIsImRvbWFpbl9saW5rIiwidXJsIiwib3IiLCJsaXRlcmFsIiwiZG9tYWluX2VtYWlsIiwiZW1haWwiLCJkb21haW5fcGFzc3dvcmQiLCJzZXJ2ZXJfbGluayIsInNlcnZlcl9lbWFpbCIsInNlcnZlcl9wYXNzd29yZCIsIndoYXRzYXBwX2dyb3VwX2xpbmsiLCJzbGFja19jaGFubmVsIiwiYnVkZ2V0IiwiYWN0dWFsX2Nvc3QiLCJQcm9qZWN0Rm9ybSIsInByb2plY3QiLCJvblN1Ym1pdCIsIm9uQ2FuY2VsIiwibG9hZGluZyIsInJlZ2lzdGVyIiwiaGFuZGxlU3VibWl0IiwiY29udHJvbCIsIndhdGNoIiwic2V0VmFsdWUiLCJmb3JtU3RhdGUiLCJlcnJvcnMiLCJpc1ZhbGlkIiwicmVzb2x2ZXIiLCJkZWZhdWx0VmFsdWVzIiwiUExBTk5JTkciLCJNRURJVU0iLCJoYW5kbGVGb3JtU3VibWl0IiwiZGF0YSIsImZvcm0iLCJjbGFzc05hbWUiLCJkaXYiLCJoMyIsImxhYmVsIiwiZXJyb3IiLCJtZXNzYWdlIiwicGxhY2Vob2xkZXIiLCJ0ZXh0YXJlYSIsInJvd3MiLCJwIiwidHlwZSIsInJlbmRlciIsImZpZWxkIiwic2VsZWN0IiwibWFwIiwib3B0aW9uIiwidmFsdWUiLCJ2YWx1ZUFzTnVtYmVyIiwic3RlcCIsInZhcmlhbnQiLCJvbkNsaWNrIiwiZGlzYWJsZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/projects/ProjectForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/projects/ProjectsFilter.tsx":
/*!****************************************************!*\
  !*** ./src/components/projects/ProjectsFilter.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectsFilter: () => (/* binding */ ProjectsFilter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types_project__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/project */ \"(ssr)/./src/types/project.ts\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectsFilter auto */ \n/**\n * Projects Filter Component - متبرمج ERP System\n * Features: Status, Priority, Date range, Search filters\n */ \n\n\n\nfunction ProjectsFilter({ filters, onFiltersChange, onClearFilters }) {\n    const [isExpanded, setIsExpanded] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const handleFilterChange = (key, value)=>{\n        onFiltersChange({\n            [key]: value\n        });\n    };\n    const hasActiveFilters = Object.values(filters).some((value)=>value !== undefined && value !== '' && value !== null);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white p-4 rounded-lg shadow-sm border space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4 space-x-reverse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"البحث في المشاريع...\",\n                                    value: filters.search || '',\n                                    onChange: (e)=>handleFilterChange('search', e.target.value),\n                                    className: \"w-full pr-10 pl-3 py-2 border border-gray-300 rounded-md text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>setIsExpanded(!isExpanded),\n                        className: \"flex items-center space-x-2 space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"فلترة\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        onClick: onClearFilters,\n                        className: \"flex items-center space-x-2 space-x-reverse text-red-600 hover:text-red-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"مسح الفلاتر\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"الحالة\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filters.status || '',\n                                onChange: (e)=>handleFilterChange('status', e.target.value || undefined),\n                                className: \"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"جميع الحالات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this),\n                                    _types_project__WEBPACK_IMPORTED_MODULE_2__.ProjectStatusOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option.value,\n                                            children: option.label\n                                        }, option.value, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"الأولوية\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filters.priority || '',\n                                onChange: (e)=>handleFilterChange('priority', e.target.value || undefined),\n                                className: \"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"جميع الأولويات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    _types_project__WEBPACK_IMPORTED_MODULE_2__.ProjectPriorityOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option.value,\n                                            children: option.label\n                                        }, option.value, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"تاريخ البداية من\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: filters.start_date_from || '',\n                                onChange: (e)=>handleFilterChange('start_date_from', e.target.value || undefined),\n                                className: \"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"تاريخ البداية إلى\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: filters.start_date_to || '',\n                                onChange: (e)=>handleFilterChange('start_date_to', e.target.value || undefined),\n                                className: \"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"تاريخ الانتهاء المتوقع من\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: filters.expected_end_date_from || '',\n                                onChange: (e)=>handleFilterChange('expected_end_date_from', e.target.value || undefined),\n                                className: \"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"تاريخ الانتهاء المتوقع إلى\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: filters.expected_end_date_to || '',\n                                onChange: (e)=>handleFilterChange('expected_end_date_to', e.target.value || undefined),\n                                className: \"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"ترتيب حسب\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filters.ordering || '-start_date',\n                                onChange: (e)=>handleFilterChange('ordering', e.target.value),\n                                className: \"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"-start_date\",\n                                        children: \"تاريخ البداية (الأحدث أولاً)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"start_date\",\n                                        children: \"تاريخ البداية (الأقدم أولاً)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"-expected_end_date\",\n                                        children: \"تاريخ الانتهاء (الأحدث أولاً)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"expected_end_date\",\n                                        children: \"تاريخ الانتهاء (الأقدم أولاً)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"name\",\n                                        children: \"الاسم (أ-ي)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"-name\",\n                                        children: \"الاسم (ي-أ)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"-priority\",\n                                        children: \"الأولوية (عالية أولاً)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"priority\",\n                                        children: \"الأولوية (منخفضة أولاً)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"-progress_percentage\",\n                                        children: \"نسبة الإنجاز (عالية أولاً)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"progress_percentage\",\n                                        children: \"نسبة الإنجاز (منخفضة أولاً)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"عدد النتائج في الصفحة\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filters.page_size || 25,\n                                onChange: (e)=>handleFilterChange('page_size', parseInt(e.target.value)),\n                                className: \"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: 10,\n                                        children: \"10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: 25,\n                                        children: \"25\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: 50,\n                                        children: \"50\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: 100,\n                                        children: \"100\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, this),\n            hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-2 pt-2 border-t\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600\",\n                        children: \"الفلاتر النشطة:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this),\n                    filters.status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800\",\n                        children: [\n                            \"الحالة: \",\n                            _types_project__WEBPACK_IMPORTED_MODULE_2__.ProjectStatusOptions.find((opt)=>opt.value === filters.status)?.label\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 13\n                    }, this),\n                    filters.priority && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800\",\n                        children: [\n                            \"الأولوية: \",\n                            _types_project__WEBPACK_IMPORTED_MODULE_2__.ProjectPriorityOptions.find((opt)=>opt.value === filters.priority)?.label\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 13\n                    }, this),\n                    filters.search && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800\",\n                        children: [\n                            \"البحث: \",\n                            filters.search\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 13\n                    }, this),\n                    filters.start_date_from && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800\",\n                        children: [\n                            \"من: \",\n                            filters.start_date_from\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 13\n                    }, this),\n                    filters.start_date_to && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800\",\n                        children: [\n                            \"إلى: \",\n                            filters.start_date_to\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsFilter.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/projects/ProjectsFilter.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/projects/ProjectsTable.tsx":
/*!***************************************************!*\
  !*** ./src/components/projects/ProjectsTable.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectsTable: () => (/* binding */ ProjectsTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-table */ \"(ssr)/./node_modules/@tanstack/table-core/build/lib/index.mjs\");\n/* harmony import */ var _tanstack_react_table__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @tanstack/react-table */ \"(ssr)/./node_modules/@tanstack/react-table/build/lib/index.mjs\");\n/* harmony import */ var _types_project__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/project */ \"(ssr)/./src/types/project.ts\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Badge */ \"(ssr)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_EyeIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,EyeIcon,PencilIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_EyeIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,EyeIcon,PencilIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_EyeIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,EyeIcon,PencilIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_EyeIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,EyeIcon,PencilIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_EyeIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,EyeIcon,PencilIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectsTable auto */ \n/**\n * Projects Table Component with TanStack Table - متبرمج ERP System\n * Features: Arabic RTL, sorting, filtering, pagination, actions\n */ \n\n\n\n\n\n\nconst columnHelper = (0,_tanstack_react_table__WEBPACK_IMPORTED_MODULE_6__.createColumnHelper)();\nfunction ProjectsTable({ projects, loading = false, onEdit, onDelete, onView }) {\n    const [sorting, setSorting] = react__WEBPACK_IMPORTED_MODULE_1___default().useState([]);\n    const [columnFilters, setColumnFilters] = react__WEBPACK_IMPORTED_MODULE_1___default().useState([]);\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProjectsTable.useMemo[columns]\": ()=>[\n                columnHelper.accessor('name', {\n                    header: 'اسم المشروع',\n                    cell: {\n                        \"ProjectsTable.useMemo[columns]\": (info)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-medium text-gray-900\",\n                                children: info.getValue()\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this)\n                    }[\"ProjectsTable.useMemo[columns]\"],\n                    enableSorting: true\n                }),\n                columnHelper.accessor('status', {\n                    header: 'الحالة',\n                    cell: {\n                        \"ProjectsTable.useMemo[columns]\": (info)=>{\n                            const status = info.getValue();\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)('text-xs', (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.getStatusColor)(status)),\n                                children: _types_project__WEBPACK_IMPORTED_MODULE_2__.ProjectStatusLabels[status]\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this);\n                        }\n                    }[\"ProjectsTable.useMemo[columns]\"],\n                    enableSorting: true,\n                    filterFn: 'equals'\n                }),\n                columnHelper.accessor('priority', {\n                    header: 'الأولوية',\n                    cell: {\n                        \"ProjectsTable.useMemo[columns]\": (info)=>{\n                            const priority = info.getValue();\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)('text-xs', (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.getPriorityColor)(priority)),\n                                children: _types_project__WEBPACK_IMPORTED_MODULE_2__.ProjectPriorityLabels[priority]\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this);\n                        }\n                    }[\"ProjectsTable.useMemo[columns]\"],\n                    enableSorting: true,\n                    filterFn: 'equals'\n                }),\n                columnHelper.accessor('progress_percentage', {\n                    header: 'نسبة الإنجاز',\n                    cell: {\n                        \"ProjectsTable.useMemo[columns]\": (info)=>{\n                            const progress = info.getValue();\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: `${progress}%`\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            progress,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this);\n                        }\n                    }[\"ProjectsTable.useMemo[columns]\"],\n                    enableSorting: true\n                }),\n                columnHelper.accessor('start_date', {\n                    header: 'تاريخ البداية',\n                    cell: {\n                        \"ProjectsTable.useMemo[columns]\": (info)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(info.getValue())\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this)\n                    }[\"ProjectsTable.useMemo[columns]\"],\n                    enableSorting: true\n                }),\n                columnHelper.accessor('expected_end_date', {\n                    header: 'تاريخ الانتهاء المتوقع',\n                    cell: {\n                        \"ProjectsTable.useMemo[columns]\": (info)=>{\n                            const date = info.getValue();\n                            const isOverdue = new Date(date) < new Date() && info.row.original.status !== _types_project__WEBPACK_IMPORTED_MODULE_2__.ProjectStatus.COMPLETED;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)('text-sm', isOverdue ? 'text-red-600 font-medium' : 'text-gray-600'),\n                                children: [\n                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(date),\n                                    isOverdue && ' (متأخر)'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this);\n                        }\n                    }[\"ProjectsTable.useMemo[columns]\"],\n                    enableSorting: true\n                }),\n                columnHelper.accessor('project_manager', {\n                    header: 'مدير المشروع',\n                    cell: {\n                        \"ProjectsTable.useMemo[columns]\": (info)=>{\n                            const manager = info.getValue();\n                            return manager ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    manager.first_name,\n                                    \" \",\n                                    manager.last_name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"غير محدد\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this);\n                        }\n                    }[\"ProjectsTable.useMemo[columns]\"],\n                    enableSorting: false\n                }),\n                columnHelper.accessor('budget', {\n                    header: 'الميزانية',\n                    cell: {\n                        \"ProjectsTable.useMemo[columns]\": (info)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(info.getValue())\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this)\n                    }[\"ProjectsTable.useMemo[columns]\"],\n                    enableSorting: true\n                }),\n                columnHelper.display({\n                    id: 'actions',\n                    header: 'الإجراءات',\n                    cell: {\n                        \"ProjectsTable.useMemo[columns]\": (info)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    onView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: {\n                                            \"ProjectsTable.useMemo[columns]\": ()=>onView(info.row.original)\n                                        }[\"ProjectsTable.useMemo[columns]\"],\n                                        className: \"h-8 w-8 p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_EyeIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this),\n                                    onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: {\n                                            \"ProjectsTable.useMemo[columns]\": ()=>onEdit(info.row.original)\n                                        }[\"ProjectsTable.useMemo[columns]\"],\n                                        className: \"h-8 w-8 p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_EyeIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    onDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: {\n                                            \"ProjectsTable.useMemo[columns]\": ()=>onDelete(info.row.original)\n                                        }[\"ProjectsTable.useMemo[columns]\"],\n                                        className: \"h-8 w-8 p-0 text-red-600 hover:text-red-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_EyeIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this)\n                    }[\"ProjectsTable.useMemo[columns]\"]\n                })\n            ]\n    }[\"ProjectsTable.useMemo[columns]\"], [\n        onEdit,\n        onDelete,\n        onView\n    ]);\n    const table = (0,_tanstack_react_table__WEBPACK_IMPORTED_MODULE_10__.useReactTable)({\n        data: projects,\n        columns,\n        state: {\n            sorting,\n            columnFilters\n        },\n        onSortingChange: setSorting,\n        onColumnFiltersChange: setColumnFilters,\n        getCoreRowModel: (0,_tanstack_react_table__WEBPACK_IMPORTED_MODULE_6__.getCoreRowModel)(),\n        getSortedRowModel: (0,_tanstack_react_table__WEBPACK_IMPORTED_MODULE_6__.getSortedRowModel)(),\n        getFilteredRowModel: (0,_tanstack_react_table__WEBPACK_IMPORTED_MODULE_6__.getFilteredRowModel)(),\n        getPaginationRowModel: (0,_tanstack_react_table__WEBPACK_IMPORTED_MODULE_6__.getPaginationRowModel)(),\n        initialState: {\n            pagination: {\n                pageSize: 10\n            }\n        }\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"mr-2 text-gray-600\",\n                    children: \"جاري التحميل...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n            lineNumber: 213,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-300\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: table.getHeaderGroups().map((headerGroup)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: headerGroup.headers.map((header)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: header.isPlaceholder ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)('flex items-center space-x-1 space-x-reverse', header.column.getCanSort() && 'cursor-pointer select-none hover:text-gray-700'),\n                                                onClick: header.column.getToggleSortingHandler(),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: (0,_tanstack_react_table__WEBPACK_IMPORTED_MODULE_10__.flexRender)(header.column.columnDef.header, header.getContext())\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    header.column.getCanSort() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_EyeIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)('h-3 w-3', header.column.getIsSorted() === 'asc' ? 'text-gray-900' : 'text-gray-400')\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_EyeIcon_PencilIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)('h-3 w-3 -mt-1', header.column.getIsSorted() === 'desc' ? 'text-gray-900' : 'text-gray-400')\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, header.id, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, this))\n                                }, headerGroup.id, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: table.getRowModel().rows.map((row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-gray-50\",\n                                    children: row.getVisibleCells().map((cell)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm\",\n                                            children: (0,_tanstack_react_table__WEBPACK_IMPORTED_MODULE_10__.flexRender)(cell.column.columnDef.cell, cell.getContext())\n                                        }, cell.id, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 19\n                                        }, this))\n                                }, row.id, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 space-x-reverse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-700\",\n                            children: [\n                                \"عرض \",\n                                table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1,\n                                \" إلى\",\n                                ' ',\n                                Math.min((table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize, table.getFilteredRowModel().rows.length),\n                                ' ',\n                                \"من \",\n                                table.getFilteredRowModel().rows.length,\n                                \" نتيجة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>table.previousPage(),\n                                disabled: !table.getCanPreviousPage(),\n                                children: \"السابق\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>table.nextPage(),\n                                disabled: !table.getCanNextPage(),\n                                children: \"التالي\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/projects/ProjectsTable.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/projects/ProjectsTable.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/QueryProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/QueryProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ QueryProvider auto */ \n/**\n * React Query Provider for متبرمج ERP System\n * Provides data fetching and caching capabilities\n */ \n\n\nfunction QueryProvider({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"QueryProvider.useState\": ()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n                defaultOptions: {\n                    queries: {\n                        // Stale time: 5 minutes\n                        staleTime: 5 * 60 * 1000,\n                        // Cache time: 10 minutes\n                        gcTime: 10 * 60 * 1000,\n                        // Retry failed requests 3 times\n                        retry: 3,\n                        // Retry delay function (exponential backoff)\n                        retryDelay: {\n                            \"QueryProvider.useState\": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)\n                        }[\"QueryProvider.useState\"],\n                        // Refetch on window focus\n                        refetchOnWindowFocus: false,\n                        // Refetch on reconnect\n                        refetchOnReconnect: true\n                    },\n                    mutations: {\n                        // Retry failed mutations once\n                        retry: 1\n                    }\n                }\n            })\n    }[\"QueryProvider.useState\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            children,\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__.ReactQueryDevtools, {\n                initialIsOpen: false\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/providers/QueryProvider.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/providers/QueryProvider.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/QueryProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/ToastProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/ToastProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider auto */ \n/**\n * Toast Provider for متبرمج ERP System\n * Provides Arabic-friendly notifications\n */ \nfunction ToastProvider() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n        position: \"top-center\",\n        reverseOrder: false,\n        gutter: 8,\n        containerClassName: \"\",\n        containerStyle: {\n            direction: 'rtl'\n        },\n        toastOptions: {\n            // Default options for all toasts\n            duration: 4000,\n            style: {\n                background: '#363636',\n                color: '#fff',\n                fontFamily: 'IBM Plex Sans Arabic, sans-serif',\n                direction: 'rtl',\n                textAlign: 'right'\n            },\n            // Success toast styling\n            success: {\n                duration: 3000,\n                style: {\n                    background: '#10b981',\n                    color: '#fff'\n                },\n                iconTheme: {\n                    primary: '#fff',\n                    secondary: '#10b981'\n                }\n            },\n            // Error toast styling\n            error: {\n                duration: 5000,\n                style: {\n                    background: '#ef4444',\n                    color: '#fff'\n                },\n                iconTheme: {\n                    primary: '#fff',\n                    secondary: '#ef4444'\n                }\n            },\n            // Loading toast styling\n            loading: {\n                style: {\n                    background: '#3b82f6',\n                    color: '#fff'\n                },\n                iconTheme: {\n                    primary: '#fff',\n                    secondary: '#3b82f6'\n                }\n            }\n        }\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/providers/ToastProvider.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/ToastProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Badge({ className, variant = 'default', ...props }) {\n    const variants = {\n        default: \"border-transparent bg-primary-600 text-text-inverse\",\n        secondary: \"border-transparent bg-secondary-100 text-text-primary\",\n        destructive: \"border-transparent bg-accent-red-600 text-text-inverse\",\n        outline: \"text-text-primary border-border-medium bg-surface\",\n        success: \"border-transparent bg-accent-green-600 text-text-inverse\",\n        warning: \"border-transparent bg-accent-orange-500 text-text-inverse\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", variants[variant], className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/ui/Badge.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9CYWRnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUNFO0FBT2hDLFNBQVNFLE1BQU0sRUFBRUMsU0FBUyxFQUFFQyxVQUFVLFNBQVMsRUFBRSxHQUFHQyxPQUFtQjtJQUNyRSxNQUFNQyxXQUFXO1FBQ2ZDLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxhQUFhO1FBQ2JDLFNBQVM7UUFDVEMsU0FBUztRQUNUQyxTQUFTO0lBQ1g7SUFFQSxxQkFDRSw4REFBQ0M7UUFDQ1YsV0FBV0YsOENBQUVBLENBQ1gsMEtBQ0FLLFFBQVEsQ0FBQ0YsUUFBUSxFQUNqQkQ7UUFFRCxHQUFHRSxLQUFLOzs7Ozs7QUFHZjtBQUVnQiIsInNvdXJjZXMiOlsiL1VzZXJzL211aGFtbWFkeW91c3NlZi9TaXRlcy9uZXh0anNfZXJwL3NyYy9jb21wb25lbnRzL3VpL0JhZGdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5leHBvcnQgaW50ZXJmYWNlIEJhZGdlUHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD4ge1xuICB2YXJpYW50PzogJ2RlZmF1bHQnIHwgJ3NlY29uZGFyeScgfCAnZGVzdHJ1Y3RpdmUnIHwgJ291dGxpbmUnIHwgJ3N1Y2Nlc3MnIHwgJ3dhcm5pbmcnXG59XG5cbmZ1bmN0aW9uIEJhZGdlKHsgY2xhc3NOYW1lLCB2YXJpYW50ID0gJ2RlZmF1bHQnLCAuLi5wcm9wcyB9OiBCYWRnZVByb3BzKSB7XG4gIGNvbnN0IHZhcmlhbnRzID0ge1xuICAgIGRlZmF1bHQ6IFwiYm9yZGVyLXRyYW5zcGFyZW50IGJnLXByaW1hcnktNjAwIHRleHQtdGV4dC1pbnZlcnNlXCIsXG4gICAgc2Vjb25kYXJ5OiBcImJvcmRlci10cmFuc3BhcmVudCBiZy1zZWNvbmRhcnktMTAwIHRleHQtdGV4dC1wcmltYXJ5XCIsXG4gICAgZGVzdHJ1Y3RpdmU6IFwiYm9yZGVyLXRyYW5zcGFyZW50IGJnLWFjY2VudC1yZWQtNjAwIHRleHQtdGV4dC1pbnZlcnNlXCIsXG4gICAgb3V0bGluZTogXCJ0ZXh0LXRleHQtcHJpbWFyeSBib3JkZXItYm9yZGVyLW1lZGl1bSBiZy1zdXJmYWNlXCIsXG4gICAgc3VjY2VzczogXCJib3JkZXItdHJhbnNwYXJlbnQgYmctYWNjZW50LWdyZWVuLTYwMCB0ZXh0LXRleHQtaW52ZXJzZVwiLFxuICAgIHdhcm5pbmc6IFwiYm9yZGVyLXRyYW5zcGFyZW50IGJnLWFjY2VudC1vcmFuZ2UtNTAwIHRleHQtdGV4dC1pbnZlcnNlXCIsXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHJvdW5kZWQtZnVsbCBib3JkZXIgcHgtMi41IHB5LTAuNSB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdHJhbnNpdGlvbi1jb2xvcnMgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXJpbmcgZm9jdXM6cmluZy1vZmZzZXQtMlwiLFxuICAgICAgICB2YXJpYW50c1t2YXJpYW50XSxcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbn1cblxuZXhwb3J0IHsgQmFkZ2UgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJCYWRnZSIsImNsYXNzTmFtZSIsInZhcmlhbnQiLCJwcm9wcyIsInZhcmlhbnRzIiwiZGVmYXVsdCIsInNlY29uZGFyeSIsImRlc3RydWN0aXZlIiwib3V0bGluZSIsInN1Y2Nlc3MiLCJ3YXJuaW5nIiwiZGl2Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant = 'default', size = 'default', loading = false, disabled, children, ...props }, ref)=>{\n    const baseClasses = \"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\";\n    const variants = {\n        default: \"bg-primary-600 text-text-inverse hover:bg-primary-700 focus:ring-primary-500\",\n        destructive: \"bg-accent-red-600 text-text-inverse hover:bg-accent-red-700 focus:ring-accent-red-500\",\n        outline: \"border border-border-medium bg-surface hover:bg-secondary-50 hover:text-text-primary text-text-primary focus:ring-primary-500\",\n        secondary: \"bg-secondary-100 text-text-primary hover:bg-secondary-200 focus:ring-secondary-500\",\n        ghost: \"hover:bg-secondary-100 hover:text-text-primary text-text-secondary focus:ring-secondary-500\",\n        link: \"text-primary-600 underline-offset-4 hover:underline focus:ring-primary-500\"\n    };\n    const sizes = {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, variants[variant], sizes[size], className),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"animate-spin -ml-1 mr-3 h-4 w-4 text-current\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/ui/Button.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/ui/Button.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/ui/Button.tsx\",\n                lineNumber: 44,\n                columnNumber: 11\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/ui/Button.tsx\",\n        lineNumber: 32,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, label, error, helperText, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: label\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/ui/Input.tsx\",\n                lineNumber: 16,\n                columnNumber: 11\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: type,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", error && \"border-red-500 focus-visible:ring-red-500\", className),\n                ref: ref,\n                ...props\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/ui/Input.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/ui/Input.tsx\",\n                lineNumber: 31,\n                columnNumber: 11\n            }, undefined),\n            helperText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-1 text-sm text-gray-500\",\n                children: helperText\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/ui/Input.tsx\",\n                lineNumber: 34,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/ui/Input.tsx\",\n        lineNumber: 14,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useProjects.ts":
/*!**********************************!*\
  !*** ./src/hooks/useProjects.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   projectsKeys: () => (/* binding */ projectsKeys),\n/* harmony export */   useCreateProject: () => (/* binding */ useCreateProject),\n/* harmony export */   useDeleteProject: () => (/* binding */ useDeleteProject),\n/* harmony export */   usePartialUpdateProject: () => (/* binding */ usePartialUpdateProject),\n/* harmony export */   useProject: () => (/* binding */ useProject),\n/* harmony export */   useProjects: () => (/* binding */ useProjects),\n/* harmony export */   useUpdateProject: () => (/* binding */ useUpdateProject)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/**\n * React Query hooks for Projects API - متبرمج ERP System\n */ \n\n\n// Query Keys\nconst projectsKeys = {\n    all: [\n        'projects'\n    ],\n    lists: ()=>[\n            ...projectsKeys.all,\n            'list'\n        ],\n    list: (filters)=>[\n            ...projectsKeys.lists(),\n            filters\n        ],\n    details: ()=>[\n            ...projectsKeys.all,\n            'detail'\n        ],\n    detail: (id)=>[\n            ...projectsKeys.details(),\n            id\n        ]\n};\n/**\n * Hook to fetch projects list with filters\n */ function useProjects(filters = {}) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)({\n        queryKey: projectsKeys.list(filters),\n        queryFn: {\n            \"useProjects.useQuery\": ()=>{\n                // Convert filters to API params\n                const params = {};\n                if (filters.status) params.status = filters.status;\n                if (filters.priority) params.priority = filters.priority;\n                if (filters.project_manager) params.project_manager = filters.project_manager.toString();\n                if (filters.start_date_from) params.start_date_from = filters.start_date_from;\n                if (filters.start_date_to) params.start_date_to = filters.start_date_to;\n                if (filters.expected_end_date_from) params.expected_end_date_from = filters.expected_end_date_from;\n                if (filters.expected_end_date_to) params.expected_end_date_to = filters.expected_end_date_to;\n                if (filters.search) params.search = filters.search;\n                if (filters.ordering) params.ordering = filters.ordering;\n                if (filters.page) params.page = filters.page.toString();\n                if (filters.page_size) params.page_size = filters.page_size.toString();\n                return _lib_api__WEBPACK_IMPORTED_MODULE_0__.projectsApi.getAll(params);\n            }\n        }[\"useProjects.useQuery\"],\n        staleTime: 5 * 60 * 1000\n    });\n}\n/**\n * Hook to fetch single project\n */ function useProject(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)({\n        queryKey: projectsKeys.detail(id),\n        queryFn: {\n            \"useProject.useQuery\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_0__.projectsApi.getById(id)\n        }[\"useProject.useQuery\"],\n        enabled: !!id\n    });\n}\n/**\n * Hook to create new project\n */ function useCreateProject() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: {\n            \"useCreateProject.useMutation\": (data)=>_lib_api__WEBPACK_IMPORTED_MODULE_0__.projectsApi.create(data)\n        }[\"useCreateProject.useMutation\"],\n        onSuccess: {\n            \"useCreateProject.useMutation\": (newProject)=>{\n                // Invalidate and refetch projects list\n                queryClient.invalidateQueries({\n                    queryKey: projectsKeys.lists()\n                });\n                // Add to cache\n                queryClient.setQueryData(projectsKeys.detail(newProject.id), newProject);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].success('تم إنشاء المشروع بنجاح');\n            }\n        }[\"useCreateProject.useMutation\"],\n        onError: {\n            \"useCreateProject.useMutation\": (error)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(error.message || 'حدث خطأ في إنشاء المشروع');\n            }\n        }[\"useCreateProject.useMutation\"]\n    });\n}\n/**\n * Hook to update project\n */ function useUpdateProject() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: {\n            \"useUpdateProject.useMutation\": ({ id, data })=>_lib_api__WEBPACK_IMPORTED_MODULE_0__.projectsApi.update(id, data)\n        }[\"useUpdateProject.useMutation\"],\n        onSuccess: {\n            \"useUpdateProject.useMutation\": (updatedProject)=>{\n                // Update in cache\n                queryClient.setQueryData(projectsKeys.detail(updatedProject.id), updatedProject);\n                // Invalidate lists to ensure consistency\n                queryClient.invalidateQueries({\n                    queryKey: projectsKeys.lists()\n                });\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].success('تم تحديث المشروع بنجاح');\n            }\n        }[\"useUpdateProject.useMutation\"],\n        onError: {\n            \"useUpdateProject.useMutation\": (error)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(error.message || 'حدث خطأ في تحديث المشروع');\n            }\n        }[\"useUpdateProject.useMutation\"]\n    });\n}\n/**\n * Hook to partially update project\n */ function usePartialUpdateProject() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: {\n            \"usePartialUpdateProject.useMutation\": ({ id, data })=>_lib_api__WEBPACK_IMPORTED_MODULE_0__.projectsApi.partialUpdate(id, data)\n        }[\"usePartialUpdateProject.useMutation\"],\n        onSuccess: {\n            \"usePartialUpdateProject.useMutation\": (updatedProject)=>{\n                // Update in cache\n                queryClient.setQueryData(projectsKeys.detail(updatedProject.id), updatedProject);\n                // Invalidate lists to ensure consistency\n                queryClient.invalidateQueries({\n                    queryKey: projectsKeys.lists()\n                });\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].success('تم تحديث المشروع بنجاح');\n            }\n        }[\"usePartialUpdateProject.useMutation\"],\n        onError: {\n            \"usePartialUpdateProject.useMutation\": (error)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(error.message || 'حدث خطأ في تحديث المشروع');\n            }\n        }[\"usePartialUpdateProject.useMutation\"]\n    });\n}\n/**\n * Hook to delete project\n */ function useDeleteProject() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: {\n            \"useDeleteProject.useMutation\": (id)=>_lib_api__WEBPACK_IMPORTED_MODULE_0__.projectsApi.delete(id)\n        }[\"useDeleteProject.useMutation\"],\n        onSuccess: {\n            \"useDeleteProject.useMutation\": (_, deletedId)=>{\n                // Remove from cache\n                queryClient.removeQueries({\n                    queryKey: projectsKeys.detail(deletedId)\n                });\n                // Invalidate lists\n                queryClient.invalidateQueries({\n                    queryKey: projectsKeys.lists()\n                });\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].success('تم حذف المشروع بنجاح');\n            }\n        }[\"useDeleteProject.useMutation\"],\n        onError: {\n            \"useDeleteProject.useMutation\": (error)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(error.message || 'حدث خطأ في حذف المشروع');\n            }\n        }[\"useDeleteProject.useMutation\"]\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useProjects.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   projectsApi: () => (/* binding */ projectsApi)\n/* harmony export */ });\n/**\n * API Client Configuration for متبرمج ERP System\n * Handles communication with Django REST API backend\n */ const API_BASE_URL = \"http://127.0.0.1:8000/api\" || 0;\n/**\n * Generic API client class\n */ class ApiClient {\n    constructor(baseURL){\n        this.token = null;\n        this.baseURL = baseURL;\n        // Get token from localStorage if available\n        if (false) {}\n    }\n    /**\n   * Set authentication token\n   */ setToken(token) {\n        this.token = token;\n        if (false) {}\n    }\n    /**\n   * Clear authentication token\n   */ clearToken() {\n        this.token = null;\n        if (false) {}\n    }\n    /**\n   * Get default headers\n   */ getHeaders() {\n        const headers = {\n            'Content-Type': 'application/json',\n            'Accept': 'application/json'\n        };\n        if (this.token) {\n            headers['Authorization'] = `Bearer ${this.token}`;\n        }\n        return headers;\n    }\n    /**\n   * Handle API response\n   */ async handleResponse(response) {\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw {\n                message: errorData.message || 'حدث خطأ في الخادم',\n                status: response.status,\n                details: errorData.details || errorData\n            };\n        }\n        return response.json();\n    }\n    /**\n   * GET request\n   */ async get(endpoint, params) {\n        const url = new URL(`${this.baseURL}${endpoint}`);\n        if (params) {\n            Object.entries(params).forEach(([key, value])=>{\n                url.searchParams.append(key, value);\n            });\n        }\n        const response = await fetch(url.toString(), {\n            method: 'GET',\n            headers: this.getHeaders()\n        });\n        return this.handleResponse(response);\n    }\n    /**\n   * POST request\n   */ async post(endpoint, data) {\n        const response = await fetch(`${this.baseURL}${endpoint}`, {\n            method: 'POST',\n            headers: this.getHeaders(),\n            body: data ? JSON.stringify(data) : undefined\n        });\n        return this.handleResponse(response);\n    }\n    /**\n   * PUT request\n   */ async put(endpoint, data) {\n        const response = await fetch(`${this.baseURL}${endpoint}`, {\n            method: 'PUT',\n            headers: this.getHeaders(),\n            body: data ? JSON.stringify(data) : undefined\n        });\n        return this.handleResponse(response);\n    }\n    /**\n   * PATCH request\n   */ async patch(endpoint, data) {\n        const response = await fetch(`${this.baseURL}${endpoint}`, {\n            method: 'PATCH',\n            headers: this.getHeaders(),\n            body: data ? JSON.stringify(data) : undefined\n        });\n        return this.handleResponse(response);\n    }\n    /**\n   * DELETE request\n   */ async delete(endpoint) {\n        const response = await fetch(`${this.baseURL}${endpoint}`, {\n            method: 'DELETE',\n            headers: this.getHeaders()\n        });\n        return this.handleResponse(response);\n    }\n}\n// Create and export API client instance\nconst apiClient = new ApiClient(API_BASE_URL);\n/**\n * Authentication API methods\n */ const authApi = {\n    login: async (credentials)=>{\n        const response = await apiClient.post('/auth/token/', credentials);\n        // Store tokens\n        apiClient.setToken(response.access);\n        if (false) {}\n        return response;\n    },\n    refresh: async ()=>{\n        const refreshToken =  false ? 0 : null;\n        if (!refreshToken) {\n            throw new Error('لا يوجد رمز تحديث');\n        }\n        const response = await apiClient.post('/auth/token/refresh/', {\n            refresh: refreshToken\n        });\n        apiClient.setToken(response.access);\n        return response;\n    },\n    logout: ()=>{\n        apiClient.clearToken();\n    }\n};\n/**\n * Projects API methods\n */ const projectsApi = {\n    getAll: (params)=>apiClient.get('/projects/', params),\n    getById: (id)=>apiClient.get(`/projects/${id}/`),\n    create: (data)=>apiClient.post('/projects/', data),\n    update: (id, data)=>apiClient.put(`/projects/${id}/`, data),\n    partialUpdate: (id, data)=>apiClient.patch(`/projects/${id}/`, data),\n    delete: (id)=>apiClient.delete(`/projects/${id}/`)\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   convertToArabicNumerals: () => (/* binding */ convertToArabicNumerals),\n/* harmony export */   convertToWesternNumerals: () => (/* binding */ convertToWesternNumerals),\n/* harmony export */   daysBetween: () => (/* binding */ daysBetween),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   getPriorityColor: () => (/* binding */ getPriorityColor),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   isOverdue: () => (/* binding */ isOverdue)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * Utility function to merge Tailwind CSS classes\n * Combines clsx and tailwind-merge for optimal class handling\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format currency in Egyptian Pounds with Arabic-Indic numerals\n */ function formatCurrency(amount) {\n    const formatted = new Intl.NumberFormat('ar-EG', {\n        style: 'currency',\n        currency: 'EGP',\n        minimumFractionDigits: 2\n    }).format(amount);\n    // Convert to Arabic-Indic numerals\n    return convertToArabicNumerals(formatted);\n}\n/**\n * Convert Western numerals to Arabic-Indic numerals\n */ function convertToArabicNumerals(text) {\n    const arabicNumerals = [\n        '٠',\n        '١',\n        '٢',\n        '٣',\n        '٤',\n        '٥',\n        '٦',\n        '٧',\n        '٨',\n        '٩'\n    ];\n    return text.replace(/[0-9]/g, (digit)=>arabicNumerals[parseInt(digit)]);\n}\n/**\n * Convert Arabic-Indic numerals to Western numerals\n */ function convertToWesternNumerals(text) {\n    const westernNumerals = [\n        '0',\n        '1',\n        '2',\n        '3',\n        '4',\n        '5',\n        '6',\n        '7',\n        '8',\n        '9'\n    ];\n    const arabicNumerals = [\n        '٠',\n        '١',\n        '٢',\n        '٣',\n        '٤',\n        '٥',\n        '٦',\n        '٧',\n        '٨',\n        '٩'\n    ];\n    return text.replace(/[٠-٩]/g, (digit)=>{\n        const index = arabicNumerals.indexOf(digit);\n        return index !== -1 ? westernNumerals[index] : digit;\n    });\n}\n/**\n * Format date in Arabic locale\n */ function formatDate(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return new Intl.DateTimeFormat('ar-EG', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    }).format(dateObj);\n}\n/**\n * Format date and time in Arabic locale\n */ function formatDateTime(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return new Intl.DateTimeFormat('ar-EG', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    }).format(dateObj);\n}\n/**\n * Calculate days between two dates\n */ function daysBetween(date1, date2) {\n    const d1 = typeof date1 === 'string' ? new Date(date1) : date1;\n    const d2 = typeof date2 === 'string' ? new Date(date2) : date2;\n    const diffTime = Math.abs(d2.getTime() - d1.getTime());\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n}\n/**\n * Check if a date is overdue (past today)\n */ function isOverdue(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    dateObj.setHours(0, 0, 0, 0);\n    return dateObj < today;\n}\n/**\n * Get status color based on status value\n */ function getStatusColor(status) {\n    const statusColors = {\n        planning: 'bg-gray-100 text-gray-800',\n        in_progress: 'bg-blue-100 text-blue-800',\n        testing: 'bg-yellow-100 text-yellow-800',\n        completed: 'bg-green-100 text-green-800',\n        on_hold: 'bg-orange-100 text-orange-800',\n        cancelled: 'bg-red-100 text-red-800'\n    };\n    return statusColors[status] || 'bg-gray-100 text-gray-800';\n}\n/**\n * Get priority color based on priority value\n */ function getPriorityColor(priority) {\n    const priorityColors = {\n        low: 'bg-green-100 text-green-800',\n        medium: 'bg-yellow-100 text-yellow-800',\n        high: 'bg-orange-100 text-orange-800',\n        urgent: 'bg-red-100 text-red-800'\n    };\n    return priorityColors[priority] || 'bg-gray-100 text-gray-800';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/project.ts":
/*!******************************!*\
  !*** ./src/types/project.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectPriority: () => (/* binding */ ProjectPriority),\n/* harmony export */   ProjectPriorityLabels: () => (/* binding */ ProjectPriorityLabels),\n/* harmony export */   ProjectPriorityOptions: () => (/* binding */ ProjectPriorityOptions),\n/* harmony export */   ProjectStatus: () => (/* binding */ ProjectStatus),\n/* harmony export */   ProjectStatusLabels: () => (/* binding */ ProjectStatusLabels),\n/* harmony export */   ProjectStatusOptions: () => (/* binding */ ProjectStatusOptions)\n/* harmony export */ });\n/**\n * TypeScript types for Project module - متبرمج ERP System\n */ var ProjectStatus = /*#__PURE__*/ function(ProjectStatus) {\n    ProjectStatus[\"PLANNING\"] = \"planning\";\n    ProjectStatus[\"IN_PROGRESS\"] = \"in_progress\";\n    ProjectStatus[\"TESTING\"] = \"testing\";\n    ProjectStatus[\"COMPLETED\"] = \"completed\";\n    ProjectStatus[\"ON_HOLD\"] = \"on_hold\";\n    ProjectStatus[\"CANCELLED\"] = \"cancelled\";\n    return ProjectStatus;\n}({});\nvar ProjectPriority = /*#__PURE__*/ function(ProjectPriority) {\n    ProjectPriority[\"LOW\"] = \"low\";\n    ProjectPriority[\"MEDIUM\"] = \"medium\";\n    ProjectPriority[\"HIGH\"] = \"high\";\n    ProjectPriority[\"URGENT\"] = \"urgent\";\n    return ProjectPriority;\n}({});\n// Status and Priority display labels in Arabic\nconst ProjectStatusLabels = {\n    [\"planning\"]: 'التخطيط',\n    [\"in_progress\"]: 'قيد التنفيذ',\n    [\"testing\"]: 'الاختبار',\n    [\"completed\"]: 'مكتمل',\n    [\"on_hold\"]: 'متوقف مؤقتاً',\n    [\"cancelled\"]: 'ملغي'\n};\nconst ProjectPriorityLabels = {\n    [\"low\"]: 'منخفضة',\n    [\"medium\"]: 'متوسطة',\n    [\"high\"]: 'عالية',\n    [\"urgent\"]: 'عاجل'\n};\n// Status and Priority options for forms\nconst ProjectStatusOptions = Object.entries(ProjectStatusLabels).map(([value, label])=>({\n        value: value,\n        label\n    }));\nconst ProjectPriorityOptions = Object.entries(ProjectPriorityLabels).map(([value, label])=>({\n        value: value,\n        label\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/project.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tanstack","vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/@fontsource","vendor-chunks/@heroicons","vendor-chunks/zod","vendor-chunks/@hookform","vendor-chunks/react-hook-form"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2Fpage&page=%2Fprojects%2Fpage&appPaths=%2Fprojects%2Fpage&pagePath=private-next-app-dir%2Fprojects%2Fpage.tsx&appDir=%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();