/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Sites/nextjs_erp/src/app/dashboard/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Sites/nextjs_erp/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Sites/nextjs_erp/src/app/dashboard/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2F%40fontsource%2Fibm-plex-sans-arabic%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2F%40fontsource%2Fibm-plex-sans-arabic%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/QueryProvider.tsx */ \"(rsc)/./src/components/providers/QueryProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ToastProvider.tsx */ \"(rsc)/./src/components/providers/ToastProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbXVoYW1tYWR5b3Vzc2VmJTJGU2l0ZXMlMkZuZXh0anNfZXJwJTJGbm9kZV9tb2R1bGVzJTJGJTQwZm9udHNvdXJjZSUyRmlibS1wbGV4LXNhbnMtYXJhYmljJTJGaW5kZXguY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbXVoYW1tYWR5b3Vzc2VmJTJGU2l0ZXMlMkZuZXh0anNfZXJwJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZtdWhhbW1hZHlvdXNzZWYlMkZTaXRlcyUyRm5leHRqc19lcnAlMkZzcmMlMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzJTJGUXVlcnlQcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJRdWVyeVByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbXVoYW1tYWR5b3Vzc2VmJTJGU2l0ZXMlMkZuZXh0anNfZXJwJTJGc3JjJTJGY29tcG9uZW50cyUyRnByb3ZpZGVycyUyRlRvYXN0UHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVG9hc3RQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb01BQTBKO0FBQzFKO0FBQ0Esb01BQTBKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJRdWVyeVByb3ZpZGVyXCJdICovIFwiL1VzZXJzL211aGFtbWFkeW91c3NlZi9TaXRlcy9uZXh0anNfZXJwL3NyYy9jb21wb25lbnRzL3Byb3ZpZGVycy9RdWVyeVByb3ZpZGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVG9hc3RQcm92aWRlclwiXSAqLyBcIi9Vc2Vycy9tdWhhbW1hZHlvdXNzZWYvU2l0ZXMvbmV4dGpzX2VycC9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvVG9hc3RQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2F%40fontsource%2Fibm-plex-sans-arabic%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbXVoYW1tYWR5b3Vzc2VmJTJGU2l0ZXMlMkZuZXh0anNfZXJwJTJGc3JjJTJGYXBwJTJGZGFzaGJvYXJkJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUF1RyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL211aGFtbWFkeW91c3NlZi9TaXRlcy9uZXh0anNfZXJwL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tdWhhbW1hZHlvdXNzZWYvU2l0ZXMvbmV4dGpzX2VycC9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Sites/nextjs_erp/src/app/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Sites/nextjs_erp/src/app/dashboard/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8f3aecd8ab37\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvbXVoYW1tYWR5b3Vzc2VmL1NpdGVzL25leHRqc19lcnAvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjhmM2FlY2Q4YWIzN1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fontsource_ibm_plex_sans_arabic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fontsource/ibm-plex-sans-arabic */ \"(rsc)/./node_modules/@fontsource/ibm-plex-sans-arabic/index.css\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_QueryProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/QueryProvider */ \"(rsc)/./src/components/providers/QueryProvider.tsx\");\n/* harmony import */ var _components_providers_ToastProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/ToastProvider */ \"(rsc)/./src/components/providers/ToastProvider.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"متبرمج - نظام إدارة المشاريع\",\n    description: \"نظام إدارة شامل للمشاريع والعملاء باللغة العربية\",\n    keywords: \"إدارة المشاريع, ERP, نظام إدارة, العربية, مشاريع, عملاء\",\n    authors: [\n        {\n            name: \"متبرمج\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-arabic antialiased bg-background text-text-primary\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_QueryProvider__WEBPACK_IMPORTED_MODULE_3__.QueryProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_ToastProvider__WEBPACK_IMPORTED_MODULE_4__.ToastProvider, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers/QueryProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/QueryProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const QueryProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Sites/nextjs_erp/src/components/providers/QueryProvider.tsx",
"QueryProvider",
);

/***/ }),

/***/ "(rsc)/./src/components/providers/ToastProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/ToastProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ToastProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ToastProvider() from the server but ToastProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Sites/nextjs_erp/src/components/providers/ToastProvider.tsx",
"ToastProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2F%40fontsource%2Fibm-plex-sans-arabic%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2F%40fontsource%2Fibm-plex-sans-arabic%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/QueryProvider.tsx */ \"(ssr)/./src/components/providers/QueryProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ToastProvider.tsx */ \"(ssr)/./src/components/providers/ToastProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbXVoYW1tYWR5b3Vzc2VmJTJGU2l0ZXMlMkZuZXh0anNfZXJwJTJGbm9kZV9tb2R1bGVzJTJGJTQwZm9udHNvdXJjZSUyRmlibS1wbGV4LXNhbnMtYXJhYmljJTJGaW5kZXguY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbXVoYW1tYWR5b3Vzc2VmJTJGU2l0ZXMlMkZuZXh0anNfZXJwJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZtdWhhbW1hZHlvdXNzZWYlMkZTaXRlcyUyRm5leHRqc19lcnAlMkZzcmMlMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzJTJGUXVlcnlQcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJRdWVyeVByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbXVoYW1tYWR5b3Vzc2VmJTJGU2l0ZXMlMkZuZXh0anNfZXJwJTJGc3JjJTJGY29tcG9uZW50cyUyRnByb3ZpZGVycyUyRlRvYXN0UHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVG9hc3RQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb01BQTBKO0FBQzFKO0FBQ0Esb01BQTBKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJRdWVyeVByb3ZpZGVyXCJdICovIFwiL1VzZXJzL211aGFtbWFkeW91c3NlZi9TaXRlcy9uZXh0anNfZXJwL3NyYy9jb21wb25lbnRzL3Byb3ZpZGVycy9RdWVyeVByb3ZpZGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVG9hc3RQcm92aWRlclwiXSAqLyBcIi9Vc2Vycy9tdWhhbW1hZHlvdXNzZWYvU2l0ZXMvbmV4dGpzX2VycC9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvVG9hc3RQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2F%40fontsource%2Fibm-plex-sans-arabic%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fcomponents%2Fproviders%2FToastProvider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbXVoYW1tYWR5b3Vzc2VmJTJGU2l0ZXMlMkZuZXh0anNfZXJwJTJGc3JjJTJGYXBwJTJGZGFzaGJvYXJkJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUF1RyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL211aGFtbWFkeW91c3NlZi9TaXRlcy9uZXh0anNfZXJwL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/MainLayout */ \"(ssr)/./src/components/layout/MainLayout.tsx\");\n/* harmony import */ var _components_dashboard_DashboardStats__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/DashboardStats */ \"(ssr)/./src/components/dashboard/DashboardStats.tsx\");\n/* harmony import */ var _components_dashboard_DashboardCharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/DashboardCharts */ \"(ssr)/./src/components/dashboard/DashboardCharts.tsx\");\n/* harmony import */ var _components_dashboard_RecentActivities__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/RecentActivities */ \"(ssr)/./src/components/dashboard/RecentActivities.tsx\");\n/* harmony import */ var _components_dashboard_QuickActions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/QuickActions */ \"(ssr)/./src/components/dashboard/QuickActions.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n/**\n * Dashboard Page - متبرمج ERP System\n * Main dashboard with statistics, charts, and recent activities\n */ \n\n\n\n\n\nfunction DashboardPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-text-primary mb-2\",\n                            children: \"لوحة التحكم الرئيسية\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/dashboard/page.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-text-secondary\",\n                            children: \"مرحباً بك في نظام إدارة متبرمج - نظرة شاملة على أداء شركتك\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/dashboard/page.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/dashboard/page.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardStats__WEBPACK_IMPORTED_MODULE_3__.DashboardStats, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/dashboard/page.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardCharts__WEBPACK_IMPORTED_MODULE_4__.DashboardCharts, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/dashboard/page.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/dashboard/page.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_QuickActions__WEBPACK_IMPORTED_MODULE_6__.QuickActions, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/dashboard/page.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/dashboard/page.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/dashboard/page.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_RecentActivities__WEBPACK_IMPORTED_MODULE_5__.RecentActivities, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/dashboard/page.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/dashboard/page.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/app/dashboard/page.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/DashboardCharts.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/DashboardCharts.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardCharts: () => (/* binding */ DashboardCharts)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ DashboardCharts auto */ \n/**\n * Dashboard Charts Component - متبرمج ERP System\n * Revenue and project charts with Arabic data\n */ \n// Mock chart data with Arabic months\nconst revenueData = [\n    {\n        month: 'يناير',\n        revenue: 180000,\n        projects: 12\n    },\n    {\n        month: 'فبراير',\n        revenue: 220000,\n        projects: 15\n    },\n    {\n        month: 'مارس',\n        revenue: 195000,\n        projects: 13\n    },\n    {\n        month: 'أبريل',\n        revenue: 280000,\n        projects: 18\n    },\n    {\n        month: 'مايو',\n        revenue: 250000,\n        projects: 16\n    },\n    {\n        month: 'يونيو',\n        revenue: 320000,\n        projects: 22\n    }\n];\nconst projectStatusData = [\n    {\n        status: 'مكتمل',\n        count: 45,\n        color: 'bg-accent-green-500'\n    },\n    {\n        status: 'قيد التنفيذ',\n        count: 32,\n        color: 'bg-primary-500'\n    },\n    {\n        status: 'متأخر',\n        count: 8,\n        color: 'bg-accent-red-500'\n    },\n    {\n        status: 'معلق',\n        count: 12,\n        color: 'bg-accent-orange-500'\n    }\n];\nfunction SimpleBarChart() {\n    const maxRevenue = Math.max(...revenueData.map((d)=>d.revenue));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-surface rounded-lg shadow-sm border border-border-light p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold text-text-primary mb-4\",\n                children: \"الإيرادات الشهرية\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: revenueData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 text-sm text-text-secondary\",\n                                children: item.month\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 mx-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-secondary-100 rounded-full h-3 overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-primary-500 h-full rounded-full transition-all duration-500\",\n                                        style: {\n                                            width: `${item.revenue / maxRevenue * 100}%`\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-24 text-sm font-medium text-text-primary text-left\",\n                                children: [\n                                    item.revenue.toLocaleString('ar-EG'),\n                                    \" ج.م\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\nfunction ProjectStatusChart() {\n    const total = projectStatusData.reduce((sum, item)=>sum + item.count, 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-surface rounded-lg shadow-sm border border-border-light p-6 mt-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold text-text-primary mb-4\",\n                children: \"حالة المشاريع\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: projectStatusData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-4 h-4 rounded-full ${item.color} ml-3`\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-text-secondary\",\n                                        children: item.status\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-text-primary ml-2\",\n                                        children: item.count\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-text-tertiary\",\n                                        children: [\n                                            \"(\",\n                                            Math.round(item.count / total * 100),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex rounded-full overflow-hidden h-3\",\n                    children: projectStatusData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: item.color,\n                            style: {\n                                width: `${item.count / total * 100}%`\n                            }\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\nfunction DashboardCharts() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleBarChart, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProjectStatusChart, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardCharts.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/DashboardCharts.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/DashboardStats.tsx":
/*!*****************************************************!*\
  !*** ./src/components/dashboard/DashboardStats.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardStats: () => (/* binding */ DashboardStats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CurrencyDollarIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CurrencyDollarIcon,FolderIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CurrencyDollarIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CurrencyDollarIcon,FolderIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CurrencyDollarIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CurrencyDollarIcon,FolderIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/FolderIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CurrencyDollarIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CurrencyDollarIcon,FolderIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CurrencyDollarIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CurrencyDollarIcon,FolderIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CurrencyDollarIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CurrencyDollarIcon,FolderIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ DashboardStats auto */ \n/**\n * Dashboard Stats Component - متبرمج ERP System\n * Key performance indicators and statistics cards\n */ \n\nfunction StatCard({ title, value, change, changeType, icon: Icon, color }) {\n    const colorClasses = {\n        primary: 'bg-primary-50 text-primary-600',\n        green: 'bg-accent-green-50 text-accent-green-600',\n        orange: 'bg-accent-orange-50 text-accent-orange-600',\n        purple: 'bg-accent-purple-50 text-accent-purple-600'\n    };\n    const changeColorClasses = changeType === 'increase' ? 'text-accent-green-600' : 'text-accent-red-600';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-surface rounded-lg shadow-sm border border-border-light p-6 hover:shadow-md transition-shadow\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium text-text-secondary mb-1\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardStats.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-3xl font-bold text-text-primary mb-2\",\n                            children: value\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardStats.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                changeType === 'increase' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CurrencyDollarIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-4 w-4 text-accent-green-600 ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardStats.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CurrencyDollarIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-4 w-4 text-accent-red-600 ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardStats.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `text-sm font-medium ${changeColorClasses}`,\n                                    children: change\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardStats.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-text-tertiary mr-2\",\n                                    children: \"من الشهر الماضي\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardStats.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardStats.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardStats.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `w-12 h-12 rounded-lg flex items-center justify-center ${colorClasses[color]}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"h-6 w-6\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardStats.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardStats.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardStats.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardStats.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\nfunction DashboardStats() {\n    const stats = [\n        {\n            title: 'إجمالي المشاريع',\n            value: '١٢٤',\n            change: '+١٢%',\n            changeType: 'increase',\n            icon: _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CurrencyDollarIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: 'primary'\n        },\n        {\n            title: 'العملاء النشطين',\n            value: '٨٧',\n            change: '+٨%',\n            changeType: 'increase',\n            icon: _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CurrencyDollarIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: 'green'\n        },\n        {\n            title: 'الإيرادات الشهرية',\n            value: '٢٥٠,٠٠٠ ج.م',\n            change: '+١٥%',\n            changeType: 'increase',\n            icon: _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CurrencyDollarIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: 'orange'\n        },\n        {\n            title: 'معدل النمو',\n            value: '٢٣%',\n            change: '-٢%',\n            changeType: 'decrease',\n            icon: _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CurrencyDollarIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: 'purple'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                ...stat\n            }, index, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardStats.tsx\",\n                lineNumber: 104,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/DashboardStats.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/DashboardStats.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/QuickActions.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/QuickActions.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuickActions: () => (/* binding */ QuickActions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_CogIcon_DocumentTextIcon_PlusIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,CogIcon,DocumentTextIcon,PlusIcon,UserPlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_CogIcon_DocumentTextIcon_PlusIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,CogIcon,DocumentTextIcon,PlusIcon,UserPlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserPlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_CogIcon_DocumentTextIcon_PlusIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,CogIcon,DocumentTextIcon,PlusIcon,UserPlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_CogIcon_DocumentTextIcon_PlusIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,CogIcon,DocumentTextIcon,PlusIcon,UserPlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_CogIcon_DocumentTextIcon_PlusIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,CogIcon,DocumentTextIcon,PlusIcon,UserPlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_CogIcon_DocumentTextIcon_PlusIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,CogIcon,DocumentTextIcon,PlusIcon,UserPlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* __next_internal_client_entry_do_not_use__ QuickActions auto */ \n/**\n * Quick Actions Component - متبرمج ERP System\n * Quick action buttons for common tasks\n */ \n\n\nfunction QuickActionCard({ title, description, icon: Icon, onClick, color }) {\n    const colorClasses = {\n        primary: 'bg-primary-50 text-primary-600 hover:bg-primary-100',\n        green: 'bg-accent-green-50 text-accent-green-600 hover:bg-accent-green-100',\n        orange: 'bg-accent-orange-50 text-accent-orange-600 hover:bg-accent-orange-100',\n        purple: 'bg-accent-purple-50 text-accent-purple-600 hover:bg-accent-purple-100'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        className: `w-full p-4 rounded-lg border border-border-light hover:border-border-medium transition-all duration-200 text-right group ${colorClasses[color]}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"font-medium text-text-primary group-hover:text-text-primary mb-1\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/QuickActions.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-text-tertiary\",\n                            children: description\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/QuickActions.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/QuickActions.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"h-6 w-6 mr-3 flex-shrink-0\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/QuickActions.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/QuickActions.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/QuickActions.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\nfunction QuickActions() {\n    const actions = [\n        {\n            title: 'مشروع جديد',\n            description: 'إنشاء مشروع جديد للعميل',\n            icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_CogIcon_DocumentTextIcon_PlusIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: 'primary',\n            onClick: ()=>console.log('إنشاء مشروع جديد')\n        },\n        {\n            title: 'إضافة عميل',\n            description: 'تسجيل عميل جديد في النظام',\n            icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_CogIcon_DocumentTextIcon_PlusIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: 'green',\n            onClick: ()=>console.log('إضافة عميل جديد')\n        },\n        {\n            title: 'تقرير مالي',\n            description: 'إنشاء تقرير مالي شهري',\n            icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_CogIcon_DocumentTextIcon_PlusIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: 'orange',\n            onClick: ()=>console.log('إنشاء تقرير مالي')\n        },\n        {\n            title: 'جدولة اجتماع',\n            description: 'حجز اجتماع مع العميل',\n            icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_CogIcon_DocumentTextIcon_PlusIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: 'purple',\n            onClick: ()=>console.log('جدولة اجتماع')\n        },\n        {\n            title: 'إنشاء فاتورة',\n            description: 'إصدار فاتورة للعميل',\n            icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_CogIcon_DocumentTextIcon_PlusIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: 'primary',\n            onClick: ()=>console.log('إنشاء فاتورة')\n        },\n        {\n            title: 'إعدادات النظام',\n            description: 'تخصيص إعدادات النظام',\n            icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_CogIcon_DocumentTextIcon_PlusIcon_UserPlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            color: 'green',\n            onClick: ()=>console.log('إعدادات النظام')\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-surface rounded-lg shadow-sm border border-border-light p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold text-text-primary mb-4\",\n                children: \"إجراءات سريعة\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/QuickActions.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: actions.map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuickActionCard, {\n                        ...action\n                    }, index, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/QuickActions.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/QuickActions.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 pt-4 border-t border-border-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"outline\",\n                    className: \"w-full\",\n                    onClick: ()=>console.log('عرض جميع الإجراءات'),\n                    children: \"عرض جميع الإجراءات\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/QuickActions.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/QuickActions.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/QuickActions.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/QuickActions.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/RecentActivities.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/RecentActivities.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentActivities: () => (/* binding */ RecentActivities)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Badge */ \"(ssr)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,FolderIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/FolderIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,FolderIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,FolderIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,FolderIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,FolderIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,FolderIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ RecentActivities auto */ \n/**\n * Recent Activities Component - متبرمج ERP System\n * Display recent activities and notifications with Arabic content\n */ \n\n\nconst activities = [\n    {\n        id: '1',\n        type: 'project',\n        title: 'تم إنشاء مشروع جديد',\n        description: 'مشروع تطوير موقع إلكتروني لشركة النور للتجارة',\n        time: 'منذ ٥ دقائق',\n        user: 'أحمد محمد',\n        status: 'success'\n    },\n    {\n        id: '2',\n        type: 'client',\n        title: 'عميل جديد',\n        description: 'تم تسجيل شركة الفجر للاستثمار كعميل جديد',\n        time: 'منذ ١٥ دقيقة',\n        user: 'فاطمة أحمد',\n        status: 'info'\n    },\n    {\n        id: '3',\n        type: 'payment',\n        title: 'تم استلام دفعة',\n        description: 'دفعة بقيمة ٥٠,٠٠٠ ج.م من شركة التقنية المتقدمة',\n        time: 'منذ ٣٠ دقيقة',\n        user: 'محمد علي',\n        status: 'success'\n    },\n    {\n        id: '4',\n        type: 'completion',\n        title: 'اكتمال مشروع',\n        description: 'تم الانتهاء من مشروع تطبيق الجوال لشركة الإبداع',\n        time: 'منذ ساعة',\n        user: 'سارة محمود',\n        status: 'success'\n    },\n    {\n        id: '5',\n        type: 'warning',\n        title: 'تحذير: مشروع متأخر',\n        description: 'مشروع موقع شركة البناء متأخر عن الموعد المحدد',\n        time: 'منذ ساعتين',\n        user: 'النظام',\n        status: 'warning'\n    },\n    {\n        id: '6',\n        type: 'document',\n        title: 'تم إنشاء فاتورة',\n        description: 'فاتورة رقم ٢٠٢٤-٠٠١ لشركة الرقمية للحلول',\n        time: 'منذ ٣ ساعات',\n        user: 'عمر حسن',\n        status: 'info'\n    }\n];\nfunction ActivityIcon({ type }) {\n    const iconClasses = \"h-5 w-5\";\n    switch(type){\n        case 'project':\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: `${iconClasses} text-primary-600`\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                lineNumber: 91,\n                columnNumber: 14\n            }, this);\n        case 'client':\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: `${iconClasses} text-accent-green-600`\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                lineNumber: 93,\n                columnNumber: 14\n            }, this);\n        case 'payment':\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: `${iconClasses} text-accent-orange-600`\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                lineNumber: 95,\n                columnNumber: 14\n            }, this);\n        case 'document':\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: `${iconClasses} text-accent-purple-600`\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                lineNumber: 97,\n                columnNumber: 14\n            }, this);\n        case 'completion':\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: `${iconClasses} text-accent-green-600`\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                lineNumber: 99,\n                columnNumber: 14\n            }, this);\n        case 'warning':\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: `${iconClasses} text-accent-red-600`\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                lineNumber: 101,\n                columnNumber: 14\n            }, this);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: `${iconClasses} text-text-tertiary`\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                lineNumber: 103,\n                columnNumber: 14\n            }, this);\n    }\n}\nfunction ActivityItem({ activity }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-start space-x-4 space-x-reverse p-4 hover:bg-secondary-50 rounded-lg transition-colors\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 w-10 h-10 bg-secondary-100 rounded-full flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActivityIcon, {\n                    type: activity.type\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-text-primary truncate\",\n                                children: activity.title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                variant: activity.status === 'success' ? 'success' : activity.status === 'warning' ? 'warning' : activity.status === 'error' ? 'destructive' : 'default',\n                                children: activity.status === 'success' ? 'مكتمل' : activity.status === 'warning' ? 'تحذير' : activity.status === 'error' ? 'خطأ' : 'معلومات'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-text-secondary mb-2\",\n                        children: activity.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between text-xs text-text-tertiary\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: activity.time\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"بواسطة: \",\n                                    activity.user\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\nfunction RecentActivities() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-surface rounded-lg shadow-sm border border-border-light\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-border-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-text-primary\",\n                            children: \"الأنشطة الأخيرة\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"text-sm text-primary-600 hover:text-primary-700 font-medium\",\n                            children: \"عرض الكل\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"divide-y divide-border-light\",\n                children: activities.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActivityItem, {\n                        activity: activity\n                    }, activity.id, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/dashboard/RecentActivities.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/RecentActivities.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n/**\n * Header Component - متبرمج ERP System\n * Top navigation bar with menu toggle and user actions\n */ \n\n\nfunction Header({ onMenuToggle }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-surface shadow-sm border-b border-border-light\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4 space-x-reverse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: onMenuToggle,\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-text-tertiary\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"البحث...\",\n                                        className: \"w-64 pr-10 pl-3 py-2 border border-border-medium rounded-md text-sm bg-surface text-text-primary placeholder:text-text-tertiary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:border-primary-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4 space-x-reverse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-6 w-6 text-text-secondary\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute -top-1 -right-1 h-4 w-4 bg-accent-red-500 text-text-inverse text-xs rounded-full flex items-center justify-center\",\n                                    children: \"3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:block text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-text-primary\",\n                                            children: \"المدير\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-text-tertiary\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-text-inverse font-medium text-sm\",\n                                        children: \"أ\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Header.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/MainLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/MainLayout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MainLayout: () => (/* binding */ MainLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/layout/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ MainLayout auto */ \n/**\n * Main Layout Component - متبرمج ERP System\n * Combines header, sidebar, and main content area\n */ \n\n\nfunction MainLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                isOpen: sidebarOpen,\n                onClose: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/MainLayout.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:mr-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_2__.Header, {\n                        onMenuToggle: ()=>setSidebarOpen(true)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/MainLayout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"bg-background\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/MainLayout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/MainLayout.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/MainLayout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/MainLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/FolderIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CurrencyDollarIcon,DocumentTextIcon,FolderIcon,HomeIcon,UserGroupIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n/**\n * Sidebar Navigation Component - متبرمج ERP System\n * Arabic RTL sidebar with navigation menu\n */ \n\n\n\n\nconst navigation = [\n    {\n        name: 'الرئيسية',\n        href: '/',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: 'لوحة التحكم',\n        href: '/dashboard',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'المشاريع',\n        href: '/projects',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'العملاء',\n        href: '/clients',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: 'المهام',\n        href: '/tasks',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: 'فريق المبيعات',\n        href: '/sales-team',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: 'مشتري الإعلانات',\n        href: '/media-buyers',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'المطورين',\n        href: '/developers',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: 'المصممين',\n        href: '/designers',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        name: 'مطوري ووردبريس',\n        href: '/wordpress-developers',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: 'التجديدات السنوية',\n        href: '/annual-renewals',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        name: 'الباقات',\n        href: '/packages',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'المصروفات',\n        href: '/expenses',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    },\n    {\n        name: 'التقارير',\n        href: '/reports',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'إعدادات النظام',\n        href: '/settings',\n        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CurrencyDollarIcon_DocumentTextIcon_FolderIcon_HomeIcon_UserGroupIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    }\n];\nfunction Sidebar({ isOpen, onClose }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-25 lg:hidden\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('fixed inset-y-0 right-0 z-50 w-64 bg-surface shadow-lg transform transition-transform duration-300 ease-in-out', isOpen ? 'translate-x-0' : 'translate-x-full lg:translate-x-0'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-16 px-4 bg-primary-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-surface rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary-600 font-bold text-lg\",\n                                            children: \"م\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-text-inverse font-bold text-xl\",\n                                        children: \"متبرمج\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-4 py-6 space-y-1 overflow-y-auto\",\n                            children: navigation.map((item)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors', isActive ? 'bg-primary-100 text-primary-700' : 'text-text-secondary hover:bg-secondary-100 hover:text-text-primary'),\n                                    onClick: ()=>onClose(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('ml-3 h-5 w-5 flex-shrink-0', isActive ? 'text-primary-500' : 'text-text-tertiary group-hover:text-text-secondary')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 19\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 p-4 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 font-medium text-sm\",\n                                            children: \"أ\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"المدير\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"<EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/layout/Sidebar.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/QueryProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/QueryProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ QueryProvider auto */ \n/**\n * React Query Provider for متبرمج ERP System\n * Provides data fetching and caching capabilities\n */ \n\n\nfunction QueryProvider({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"QueryProvider.useState\": ()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n                defaultOptions: {\n                    queries: {\n                        // Stale time: 5 minutes\n                        staleTime: 5 * 60 * 1000,\n                        // Cache time: 10 minutes\n                        gcTime: 10 * 60 * 1000,\n                        // Retry failed requests 3 times\n                        retry: 3,\n                        // Retry delay function (exponential backoff)\n                        retryDelay: {\n                            \"QueryProvider.useState\": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)\n                        }[\"QueryProvider.useState\"],\n                        // Refetch on window focus\n                        refetchOnWindowFocus: false,\n                        // Refetch on reconnect\n                        refetchOnReconnect: true\n                    },\n                    mutations: {\n                        // Retry failed mutations once\n                        retry: 1\n                    }\n                }\n            })\n    }[\"QueryProvider.useState\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            children,\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__.ReactQueryDevtools, {\n                initialIsOpen: false\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/providers/QueryProvider.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/providers/QueryProvider.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/QueryProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/ToastProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/ToastProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider auto */ \n/**\n * Toast Provider for متبرمج ERP System\n * Provides Arabic-friendly notifications\n */ \nfunction ToastProvider() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n        position: \"top-center\",\n        reverseOrder: false,\n        gutter: 8,\n        containerClassName: \"\",\n        containerStyle: {\n            direction: 'rtl'\n        },\n        toastOptions: {\n            // Default options for all toasts\n            duration: 4000,\n            style: {\n                background: '#363636',\n                color: '#fff',\n                fontFamily: 'IBM Plex Sans Arabic, sans-serif',\n                direction: 'rtl',\n                textAlign: 'right'\n            },\n            // Success toast styling\n            success: {\n                duration: 3000,\n                style: {\n                    background: '#10b981',\n                    color: '#fff'\n                },\n                iconTheme: {\n                    primary: '#fff',\n                    secondary: '#10b981'\n                }\n            },\n            // Error toast styling\n            error: {\n                duration: 5000,\n                style: {\n                    background: '#ef4444',\n                    color: '#fff'\n                },\n                iconTheme: {\n                    primary: '#fff',\n                    secondary: '#ef4444'\n                }\n            },\n            // Loading toast styling\n            loading: {\n                style: {\n                    background: '#3b82f6',\n                    color: '#fff'\n                },\n                iconTheme: {\n                    primary: '#fff',\n                    secondary: '#3b82f6'\n                }\n            }\n        }\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/providers/ToastProvider.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/ToastProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Badge({ className, variant = 'default', ...props }) {\n    const variants = {\n        default: \"border-transparent bg-primary-600 text-text-inverse\",\n        secondary: \"border-transparent bg-secondary-100 text-text-primary\",\n        destructive: \"border-transparent bg-accent-red-600 text-text-inverse\",\n        outline: \"text-text-primary border-border-medium bg-surface\",\n        success: \"border-transparent bg-accent-green-600 text-text-inverse\",\n        warning: \"border-transparent bg-accent-orange-500 text-text-inverse\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", variants[variant], className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/ui/Badge.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant = 'default', size = 'default', loading = false, disabled, children, ...props }, ref)=>{\n    const baseClasses = \"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\";\n    const variants = {\n        default: \"bg-primary-600 text-text-inverse hover:bg-primary-700 focus:ring-primary-500\",\n        destructive: \"bg-accent-red-600 text-text-inverse hover:bg-accent-red-700 focus:ring-accent-red-500\",\n        outline: \"border border-border-medium bg-surface hover:bg-secondary-50 hover:text-text-primary text-text-primary focus:ring-primary-500\",\n        secondary: \"bg-secondary-100 text-text-primary hover:bg-secondary-200 focus:ring-secondary-500\",\n        ghost: \"hover:bg-secondary-100 hover:text-text-primary text-text-secondary focus:ring-secondary-500\",\n        link: \"text-primary-600 underline-offset-4 hover:underline focus:ring-primary-500\"\n    };\n    const sizes = {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, variants[variant], sizes[size], className),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"animate-spin -ml-1 mr-3 h-4 w-4 text-current\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/ui/Button.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/ui/Button.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/ui/Button.tsx\",\n                lineNumber: 44,\n                columnNumber: 11\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Sites/nextjs_erp/src/components/ui/Button.tsx\",\n        lineNumber: 32,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   convertToArabicNumerals: () => (/* binding */ convertToArabicNumerals),\n/* harmony export */   convertToWesternNumerals: () => (/* binding */ convertToWesternNumerals),\n/* harmony export */   daysBetween: () => (/* binding */ daysBetween),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   getPriorityColor: () => (/* binding */ getPriorityColor),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   isOverdue: () => (/* binding */ isOverdue)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * Utility function to merge Tailwind CSS classes\n * Combines clsx and tailwind-merge for optimal class handling\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format currency in Egyptian Pounds with Arabic-Indic numerals\n */ function formatCurrency(amount) {\n    const formatted = new Intl.NumberFormat('ar-EG', {\n        style: 'currency',\n        currency: 'EGP',\n        minimumFractionDigits: 2\n    }).format(amount);\n    // Convert to Arabic-Indic numerals\n    return convertToArabicNumerals(formatted);\n}\n/**\n * Convert Western numerals to Arabic-Indic numerals\n */ function convertToArabicNumerals(text) {\n    const arabicNumerals = [\n        '٠',\n        '١',\n        '٢',\n        '٣',\n        '٤',\n        '٥',\n        '٦',\n        '٧',\n        '٨',\n        '٩'\n    ];\n    return text.replace(/[0-9]/g, (digit)=>arabicNumerals[parseInt(digit)]);\n}\n/**\n * Convert Arabic-Indic numerals to Western numerals\n */ function convertToWesternNumerals(text) {\n    const westernNumerals = [\n        '0',\n        '1',\n        '2',\n        '3',\n        '4',\n        '5',\n        '6',\n        '7',\n        '8',\n        '9'\n    ];\n    const arabicNumerals = [\n        '٠',\n        '١',\n        '٢',\n        '٣',\n        '٤',\n        '٥',\n        '٦',\n        '٧',\n        '٨',\n        '٩'\n    ];\n    return text.replace(/[٠-٩]/g, (digit)=>{\n        const index = arabicNumerals.indexOf(digit);\n        return index !== -1 ? westernNumerals[index] : digit;\n    });\n}\n/**\n * Format date in Arabic locale\n */ function formatDate(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return new Intl.DateTimeFormat('ar-EG', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    }).format(dateObj);\n}\n/**\n * Format date and time in Arabic locale\n */ function formatDateTime(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return new Intl.DateTimeFormat('ar-EG', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    }).format(dateObj);\n}\n/**\n * Calculate days between two dates\n */ function daysBetween(date1, date2) {\n    const d1 = typeof date1 === 'string' ? new Date(date1) : date1;\n    const d2 = typeof date2 === 'string' ? new Date(date2) : date2;\n    const diffTime = Math.abs(d2.getTime() - d1.getTime());\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n}\n/**\n * Check if a date is overdue (past today)\n */ function isOverdue(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    dateObj.setHours(0, 0, 0, 0);\n    return dateObj < today;\n}\n/**\n * Get status color based on status value\n */ function getStatusColor(status) {\n    const statusColors = {\n        planning: 'bg-gray-100 text-gray-800',\n        in_progress: 'bg-blue-100 text-blue-800',\n        testing: 'bg-yellow-100 text-yellow-800',\n        completed: 'bg-green-100 text-green-800',\n        on_hold: 'bg-orange-100 text-orange-800',\n        cancelled: 'bg-red-100 text-red-800'\n    };\n    return statusColors[status] || 'bg-gray-100 text-gray-800';\n}\n/**\n * Get priority color based on priority value\n */ function getPriorityColor(priority) {\n    const priorityColors = {\n        low: 'bg-green-100 text-green-800',\n        medium: 'bg-yellow-100 text-yellow-800',\n        high: 'bg-orange-100 text-orange-800',\n        urgent: 'bg-red-100 text-red-800'\n    };\n    return priorityColors[priority] || 'bg-gray-100 text-gray-800';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tanstack","vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/@fontsource","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmuhammadyoussef%2FSites%2Fnextjs_erp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();