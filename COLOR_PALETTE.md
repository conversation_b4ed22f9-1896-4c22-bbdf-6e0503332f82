# متبرمج ERP - نظام الألوان الموحد

## نظرة عامة
تم تطوير نظام ألوان شامل ومتسق لنظام إدارة متبرمج ERP، مع التركيز على إمكانية الوصول والتباين المناسب للمحتوى العربي.

## الألوان الأساسية (Primary Colors)
مستخرجة من شعار الشركة وتستخدم للعناصر الرئيسية والأزرار الأساسية:

- `--primary-50`: #eff6ff (خلفية فاتحة جداً)
- `--primary-100`: #dbeafe (خلفية فاتحة)
- `--primary-200`: #bfdbfe (حدود فاتحة)
- `--primary-300`: #93c5fd (عناصر ثانوية)
- `--primary-400`: #60a5fa (عناصر تفاعلية)
- `--primary-500`: #3b82f6 (اللون الأساسي)
- `--primary-600`: #2563eb (الأزرار الأساسية)
- `--primary-700`: #1d4ed8 (حالة التحويم)
- `--primary-800`: #1e40af (نص على خلفية فاتحة)
- `--primary-900`: #1e3a8a (نص داكن)
- `--primary-950`: #172554 (أغمق درجة)

## الألوان الثانوية (Secondary Colors)
للخلفيات والعناصر المساعدة:

- `--secondary-50`: #f8fafc
- `--secondary-100`: #f1f5f9
- `--secondary-200`: #e2e8f0
- `--secondary-300`: #cbd5e1
- `--secondary-400`: #94a3b8
- `--secondary-500`: #64748b
- `--secondary-600`: #475569
- `--secondary-700`: #334155
- `--secondary-800`: #1e293b
- `--secondary-900`: #0f172a

## ألوان التمييز (Accent Colors)

### الأخضر (للنجاح والإيجابية)
- `--accent-green-50`: #f0fdf4
- `--accent-green-500`: #22c55e
- `--accent-green-600`: #16a34a
- `--accent-green-700`: #15803d

### البرتقالي (للتحذيرات والإشعارات)
- `--accent-orange-50`: #fff7ed
- `--accent-orange-500`: #f97316
- `--accent-orange-600`: #ea580c
- `--accent-orange-700`: #c2410c

### الأحمر (للأخطاء والحذف)
- `--accent-red-50`: #fef2f2
- `--accent-red-500`: #ef4444
- `--accent-red-600`: #dc2626
- `--accent-red-700`: #b91c1c

### البنفسجي (للميزات المتقدمة)
- `--accent-purple-50`: #faf5ff
- `--accent-purple-500`: #a855f7
- `--accent-purple-600`: #9333ea
- `--accent-purple-700`: #7c3aed

## الألوان الدلالية (Semantic Colors)
- `--success`: var(--accent-green-600) - للعمليات الناجحة
- `--warning`: var(--accent-orange-500) - للتحذيرات
- `--error`: var(--accent-red-600) - للأخطاء
- `--info`: var(--primary-600) - للمعلومات

## ألوان الخلفيات والأسطح
- `--background`: var(--neutral-gray-50) - خلفية الصفحة الرئيسية
- `--surface`: var(--neutral-white) - خلفية البطاقات والنوافذ
- `--surface-elevated`: var(--neutral-white) - العناصر المرفوعة

## ألوان النصوص
- `--text-primary`: var(--neutral-gray-900) - النص الأساسي
- `--text-secondary`: var(--neutral-gray-600) - النص الثانوي
- `--text-tertiary`: var(--neutral-gray-500) - النص المساعد
- `--text-inverse`: var(--neutral-white) - النص على خلفية داكنة

## ألوان الحدود
- `--border-light`: var(--neutral-gray-200) - حدود فاتحة
- `--border-medium`: var(--neutral-gray-300) - حدود متوسطة
- `--border-strong`: var(--neutral-gray-400) - حدود قوية

## نسب التباين (WCAG Compliance)
جميع الألوان تلتزم بمعايير إمكانية الوصول:

- **النص العادي**: نسبة تباين 4.5:1 كحد أدنى
- **النص الكبير**: نسبة تباين 3:1 كحد أدنى
- **العناصر التفاعلية**: نسبة تباين 3:1 كحد أدنى

## استخدام الألوان في المكونات

### الأزرار
- **الأساسي**: `bg-primary-600 text-text-inverse hover:bg-primary-700`
- **الثانوي**: `bg-secondary-100 text-text-primary hover:bg-secondary-200`
- **المخطط**: `border-border-medium bg-surface hover:bg-secondary-50`
- **الخطر**: `bg-accent-red-600 text-text-inverse hover:bg-accent-red-700`

### البطاقات والأسطح
- **البطاقة العادية**: `bg-surface border-border-light shadow-sm`
- **البطاقة المرفوعة**: `bg-surface-elevated border-border-light shadow-md`

### الحالات
- **النجاح**: `bg-accent-green-50 text-accent-green-800 border-accent-green-200`
- **التحذير**: `bg-accent-orange-50 text-accent-orange-800 border-accent-orange-200`
- **الخطأ**: `bg-accent-red-50 text-accent-red-800 border-accent-red-200`
- **المعلومات**: `bg-primary-50 text-primary-800 border-primary-200`

## الوضع المظلم
يتم تطبيق الوضع المظلم تلقائياً باستخدام `prefers-color-scheme: dark`:

- الخلفيات تصبح داكنة
- النصوص تصبح فاتحة
- الحدود تتكيف مع الوضع المظلم
- الألوان الأساسية تبقى ثابتة للحفاظ على الهوية البصرية

## أمثلة الاستخدام

```css
/* زر أساسي */
.btn-primary {
  background-color: var(--primary-600);
  color: var(--text-inverse);
}

/* بطاقة */
.card {
  background-color: var(--surface);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
}

/* نص ثانوي */
.text-secondary {
  color: var(--text-secondary);
}
```

## ملاحظات للمطورين
1. استخدم دائماً متغيرات CSS بدلاً من القيم المباشرة
2. تأكد من اختبار التباين قبل إضافة ألوان جديدة
3. استخدم الألوان الدلالية للحالات المختلفة
4. اختبر التصميم في الوضعين الفاتح والمظلم
