'use client'

/**
 * Quick Actions Component - متبرمج ERP System
 * Quick action buttons for common tasks
 */

import React from 'react'
import { Button } from '@/components/ui/Button'
import { 
  PlusIcon, 
  DocumentTextIcon, 
  UserPlusIcon, 
  ChartBarIcon,
  CogIcon,
  CalendarIcon
} from '@heroicons/react/24/outline'

interface QuickActionProps {
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  onClick: () => void
  color: 'primary' | 'green' | 'orange' | 'purple'
}

function QuickActionCard({ title, description, icon: Icon, onClick, color }: QuickActionProps) {
  const colorClasses = {
    primary: 'bg-primary-50 text-primary-600 hover:bg-primary-100',
    green: 'bg-accent-green-50 text-accent-green-600 hover:bg-accent-green-100',
    orange: 'bg-accent-orange-50 text-accent-orange-600 hover:bg-accent-orange-100',
    purple: 'bg-accent-purple-50 text-accent-purple-600 hover:bg-accent-purple-100',
  }

  return (
    <button
      onClick={onClick}
      className={`w-full p-4 rounded-lg border border-border-light hover:border-border-medium transition-all duration-200 text-right group ${colorClasses[color]}`}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h4 className="font-medium text-text-primary group-hover:text-text-primary mb-1">
            {title}
          </h4>
          <p className="text-sm text-text-tertiary">
            {description}
          </p>
        </div>
        <Icon className="h-6 w-6 mr-3 flex-shrink-0" />
      </div>
    </button>
  )
}

export function QuickActions() {
  const actions = [
    {
      title: 'مشروع جديد',
      description: 'إنشاء مشروع جديد للعميل',
      icon: PlusIcon,
      color: 'primary' as const,
      onClick: () => console.log('إنشاء مشروع جديد'),
    },
    {
      title: 'إضافة عميل',
      description: 'تسجيل عميل جديد في النظام',
      icon: UserPlusIcon,
      color: 'green' as const,
      onClick: () => console.log('إضافة عميل جديد'),
    },
    {
      title: 'تقرير مالي',
      description: 'إنشاء تقرير مالي شهري',
      icon: ChartBarIcon,
      color: 'orange' as const,
      onClick: () => console.log('إنشاء تقرير مالي'),
    },
    {
      title: 'جدولة اجتماع',
      description: 'حجز اجتماع مع العميل',
      icon: CalendarIcon,
      color: 'purple' as const,
      onClick: () => console.log('جدولة اجتماع'),
    },
    {
      title: 'إنشاء فاتورة',
      description: 'إصدار فاتورة للعميل',
      icon: DocumentTextIcon,
      color: 'primary' as const,
      onClick: () => console.log('إنشاء فاتورة'),
    },
    {
      title: 'إعدادات النظام',
      description: 'تخصيص إعدادات النظام',
      icon: CogIcon,
      color: 'green' as const,
      onClick: () => console.log('إعدادات النظام'),
    },
  ]

  return (
    <div className="bg-surface rounded-lg shadow-sm border border-border-light p-6">
      <h3 className="text-lg font-semibold text-text-primary mb-4">إجراءات سريعة</h3>
      <div className="space-y-3">
        {actions.map((action, index) => (
          <QuickActionCard key={index} {...action} />
        ))}
      </div>
      
      {/* Additional Action Button */}
      <div className="mt-6 pt-4 border-t border-border-light">
        <Button 
          variant="outline" 
          className="w-full"
          onClick={() => console.log('عرض جميع الإجراءات')}
        >
          عرض جميع الإجراءات
        </Button>
      </div>
    </div>
  )
}
