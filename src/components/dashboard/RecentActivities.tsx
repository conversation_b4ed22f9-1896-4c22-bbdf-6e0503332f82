'use client'

/**
 * Recent Activities Component - متبرمج ERP System
 * Display recent activities and notifications with Arabic content
 */

import React from 'react'
import { Badge } from '@/components/ui/Badge'
import { 
  FolderIcon, 
  UsersIcon, 
  CurrencyDollarIcon, 
  DocumentTextIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

interface Activity {
  id: string
  type: 'project' | 'client' | 'payment' | 'document' | 'completion' | 'warning'
  title: string
  description: string
  time: string
  user: string
  status: 'success' | 'warning' | 'info' | 'error'
}

const activities: Activity[] = [
  {
    id: '1',
    type: 'project',
    title: 'تم إنشاء مشروع جديد',
    description: 'مشروع تطوير موقع إلكتروني لشركة النور للتجارة',
    time: 'منذ ٥ دقائق',
    user: 'أحمد محمد',
    status: 'success',
  },
  {
    id: '2',
    type: 'client',
    title: 'عميل جديد',
    description: 'تم تسجيل شركة الفجر للاستثمار كعميل جديد',
    time: 'منذ ١٥ دقيقة',
    user: 'فاطمة أحمد',
    status: 'info',
  },
  {
    id: '3',
    type: 'payment',
    title: 'تم استلام دفعة',
    description: 'دفعة بقيمة ٥٠,٠٠٠ ج.م من شركة التقنية المتقدمة',
    time: 'منذ ٣٠ دقيقة',
    user: 'محمد علي',
    status: 'success',
  },
  {
    id: '4',
    type: 'completion',
    title: 'اكتمال مشروع',
    description: 'تم الانتهاء من مشروع تطبيق الجوال لشركة الإبداع',
    time: 'منذ ساعة',
    user: 'سارة محمود',
    status: 'success',
  },
  {
    id: '5',
    type: 'warning',
    title: 'تحذير: مشروع متأخر',
    description: 'مشروع موقع شركة البناء متأخر عن الموعد المحدد',
    time: 'منذ ساعتين',
    user: 'النظام',
    status: 'warning',
  },
  {
    id: '6',
    type: 'document',
    title: 'تم إنشاء فاتورة',
    description: 'فاتورة رقم ٢٠٢٤-٠٠١ لشركة الرقمية للحلول',
    time: 'منذ ٣ ساعات',
    user: 'عمر حسن',
    status: 'info',
  },
]

function ActivityIcon({ type }: { type: Activity['type'] }) {
  const iconClasses = "h-5 w-5"
  
  switch (type) {
    case 'project':
      return <FolderIcon className={`${iconClasses} text-primary-600`} />
    case 'client':
      return <UsersIcon className={`${iconClasses} text-accent-green-600`} />
    case 'payment':
      return <CurrencyDollarIcon className={`${iconClasses} text-accent-orange-600`} />
    case 'document':
      return <DocumentTextIcon className={`${iconClasses} text-accent-purple-600`} />
    case 'completion':
      return <CheckCircleIcon className={`${iconClasses} text-accent-green-600`} />
    case 'warning':
      return <ExclamationTriangleIcon className={`${iconClasses} text-accent-red-600`} />
    default:
      return <FolderIcon className={`${iconClasses} text-text-tertiary`} />
  }
}

function ActivityItem({ activity }: { activity: Activity }) {
  return (
    <div className="flex items-start space-x-4 space-x-reverse p-4 hover:bg-secondary-50 rounded-lg transition-colors">
      <div className="flex-shrink-0 w-10 h-10 bg-secondary-100 rounded-full flex items-center justify-center">
        <ActivityIcon type={activity.type} />
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-1">
          <h4 className="text-sm font-medium text-text-primary truncate">
            {activity.title}
          </h4>
          <Badge variant={activity.status === 'success' ? 'success' : 
                         activity.status === 'warning' ? 'warning' : 
                         activity.status === 'error' ? 'destructive' : 'default'}>
            {activity.status === 'success' ? 'مكتمل' :
             activity.status === 'warning' ? 'تحذير' :
             activity.status === 'error' ? 'خطأ' : 'معلومات'}
          </Badge>
        </div>
        
        <p className="text-sm text-text-secondary mb-2">
          {activity.description}
        </p>
        
        <div className="flex items-center justify-between text-xs text-text-tertiary">
          <span>{activity.time}</span>
          <span>بواسطة: {activity.user}</span>
        </div>
      </div>
    </div>
  )
}

export function RecentActivities() {
  return (
    <div className="bg-surface rounded-lg shadow-sm border border-border-light">
      <div className="p-6 border-b border-border-light">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-text-primary">الأنشطة الأخيرة</h3>
          <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
            عرض الكل
          </button>
        </div>
      </div>
      
      <div className="divide-y divide-border-light">
        {activities.map((activity) => (
          <ActivityItem key={activity.id} activity={activity} />
        ))}
      </div>
    </div>
  )
}
