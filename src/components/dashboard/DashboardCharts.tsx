'use client'

/**
 * Dashboard Charts Component - متبرمج ERP System
 * Revenue and project charts with Arabic data
 */

import React from 'react'

// Mock chart data with Arabic months
const revenueData = [
  { month: 'يناير', revenue: 180000, projects: 12 },
  { month: 'فبراير', revenue: 220000, projects: 15 },
  { month: 'مارس', revenue: 195000, projects: 13 },
  { month: 'أبريل', revenue: 280000, projects: 18 },
  { month: 'مايو', revenue: 250000, projects: 16 },
  { month: 'يونيو', revenue: 320000, projects: 22 },
]

const projectStatusData = [
  { status: 'مكتمل', count: 45, color: 'bg-accent-green-500' },
  { status: 'قيد التنفيذ', count: 32, color: 'bg-primary-500' },
  { status: 'متأخر', count: 8, color: 'bg-accent-red-500' },
  { status: 'معلق', count: 12, color: 'bg-accent-orange-500' },
]

function SimpleBarChart() {
  const maxRevenue = Math.max(...revenueData.map(d => d.revenue))
  
  return (
    <div className="bg-surface rounded-lg shadow-sm border border-border-light p-6">
      <h3 className="text-lg font-semibold text-text-primary mb-4">الإيرادات الشهرية</h3>
      <div className="space-y-4">
        {revenueData.map((item, index) => (
          <div key={index} className="flex items-center">
            <div className="w-16 text-sm text-text-secondary">{item.month}</div>
            <div className="flex-1 mx-4">
              <div className="bg-secondary-100 rounded-full h-3 overflow-hidden">
                <div 
                  className="bg-primary-500 h-full rounded-full transition-all duration-500"
                  style={{ width: `${(item.revenue / maxRevenue) * 100}%` }}
                />
              </div>
            </div>
            <div className="w-24 text-sm font-medium text-text-primary text-left">
              {item.revenue.toLocaleString('ar-EG')} ج.م
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

function ProjectStatusChart() {
  const total = projectStatusData.reduce((sum, item) => sum + item.count, 0)
  
  return (
    <div className="bg-surface rounded-lg shadow-sm border border-border-light p-6 mt-6">
      <h3 className="text-lg font-semibold text-text-primary mb-4">حالة المشاريع</h3>
      <div className="space-y-4">
        {projectStatusData.map((item, index) => (
          <div key={index} className="flex items-center justify-between">
            <div className="flex items-center">
              <div className={`w-4 h-4 rounded-full ${item.color} ml-3`} />
              <span className="text-sm text-text-secondary">{item.status}</span>
            </div>
            <div className="flex items-center">
              <span className="text-sm font-medium text-text-primary ml-2">
                {item.count}
              </span>
              <span className="text-xs text-text-tertiary">
                ({Math.round((item.count / total) * 100)}%)
              </span>
            </div>
          </div>
        ))}
      </div>
      
      {/* Simple progress bar visualization */}
      <div className="mt-6">
        <div className="flex rounded-full overflow-hidden h-3">
          {projectStatusData.map((item, index) => (
            <div
              key={index}
              className={item.color}
              style={{ width: `${(item.count / total) * 100}%` }}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

export function DashboardCharts() {
  return (
    <div>
      <SimpleBarChart />
      <ProjectStatusChart />
    </div>
  )
}
