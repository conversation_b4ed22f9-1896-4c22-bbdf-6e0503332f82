import type { Metadata } from "next";
import "@fontsource/ibm-plex-sans-arabic";
import "./globals.css";
import { QueryProvider } from "@/components/providers/QueryProvider";
import { ToastProvider } from "@/components/providers/ToastProvider";

export const metadata: Metadata = {
  title: "متبرمج - نظام إدارة المشاريع",
  description: "نظام إدارة شامل للمشاريع والعملاء باللغة العربية",
  keywords: "إدارة المشاريع, ERP, نظام إدارة, العربية, مشاريع, عملاء",
  authors: [{ name: "متبرمج" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <body className="font-arabic antialiased bg-background text-text-primary">
        <QueryProvider>
          {children}
          <ToastProvider />
        </QueryProvider>
      </body>
    </html>
  );
}
