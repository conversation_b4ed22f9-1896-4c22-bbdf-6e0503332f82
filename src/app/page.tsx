import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/Button";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100 flex flex-col items-center justify-center p-8">
      <main className="text-center max-w-4xl mx-auto">
        {/* Logo Section */}
        <div className="mb-8">
          <Image
            src="/the_logo.png"
            alt="شعار متبرمج"
            width={200}
            height={200}
            priority
            className="mx-auto rounded-lg shadow-lg"
          />
        </div>

        {/* Welcome Section */}
        <div className="mb-12">
          <h1 className="text-5xl font-bold text-text-primary mb-4">
            مرحباً بك في متبرمج
          </h1>
          <p className="text-xl text-text-secondary mb-6">
            نظام إدارة شامل للمشاريع والعملاء باللغة العربية
          </p>
          <div className="bg-surface rounded-lg shadow-md border border-border-light p-6 mb-8">
            <h2 className="text-2xl font-semibold text-text-primary mb-4">
              الميزات الرئيسية
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-right">
              <div className="p-4 bg-primary-50 rounded-lg border border-primary-200">
                <h3 className="font-semibold text-primary-800">إدارة المشاريع</h3>
                <p className="text-primary-600 text-sm">تتبع شامل لجميع المشاريع</p>
              </div>
              <div className="p-4 bg-accent-green-50 rounded-lg border border-accent-green-200">
                <h3 className="font-semibold text-accent-green-800">إدارة العملاء</h3>
                <p className="text-accent-green-600 text-sm">قاعدة بيانات متكاملة للعملاء</p>
              </div>
              <div className="p-4 bg-accent-purple-50 rounded-lg border border-accent-purple-200">
                <h3 className="font-semibold text-accent-purple-800">التقارير</h3>
                <p className="text-accent-purple-600 text-sm">تقارير تفصيلية وإحصائيات</p>
              </div>
            </div>
          </div>
        </div>

        {/* Currency Display Test */}
        <div className="bg-surface rounded-lg shadow-md border border-border-light p-6 mb-8">
          <h3 className="text-lg font-semibold text-text-primary mb-4">عرض العملة المصرية</h3>
          <div className="text-2xl font-bold text-accent-green-600">
            <span className="currency-egp">١٢٣٤٥</span>
          </div>
          <p className="text-sm text-text-tertiary mt-2">
            الأرقام العربية الهندية مع رمز الجنيه المصري
          </p>
        </div>

        {/* Quick Actions */}
        <div className="bg-surface rounded-lg shadow-md border border-border-light p-6 mb-8">
          <h3 className="text-lg font-semibold text-text-primary mb-4">الإجراءات السريعة</h3>
          <div className="flex flex-wrap gap-4 justify-center">
            <Link href="/dashboard">
              <Button size="lg" className="flex items-center space-x-2 space-x-reverse">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2V7" />
                </svg>
                <span>لوحة التحكم</span>
              </Button>
            </Link>
            <Link href="/projects">
              <Button variant="outline" size="lg" className="flex items-center space-x-2 space-x-reverse">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <span>إدارة المشاريع</span>
              </Button>
            </Link>
            <Button variant="secondary" size="lg" disabled>
              <span>إدارة العملاء</span>
              <span className="text-xs text-text-tertiary mr-2">(قريباً)</span>
            </Button>
          </div>
        </div>

        {/* Status */}
        <div className="bg-accent-green-50 border border-accent-green-200 rounded-lg p-4">
          <p className="text-accent-green-800">
            ✅ تم تحديث النظام بنظام الألوان الجديد - لوحة التحكم متاحة الآن!
          </p>
        </div>
      </main>
    </div>
  );
}
