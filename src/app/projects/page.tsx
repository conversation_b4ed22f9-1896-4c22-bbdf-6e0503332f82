'use client'

/**
 * Projects Page - متبرمج ERP System
 * Main projects management interface with table, filters, and CRUD operations
 */

import React, { useState, useEffect } from 'react'
import { useProjects, useCreateProject, useUpdateProject, useDeleteProject } from '@/hooks/useProjects'
import { ProjectsTable } from '@/components/projects/ProjectsTable'
import { ProjectsFilter } from '@/components/projects/ProjectsFilter'
import { ProjectForm } from '@/components/projects/ProjectForm'
import { MainLayout } from '@/components/layout/MainLayout'
import { Button } from '@/components/ui/Button'
import { Project, ProjectFilters, ProjectFormData } from '@/types/project'
import { PlusIcon, DocumentArrowDownIcon } from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

// Modal Component
function Modal({ isOpen, onClose, title, children }: {
  isOpen: boolean
  onClose: () => void
  title: string
  children: React.ReactNode
}) {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={onClose} />
        <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <span className="sr-only">إغلاق</span>
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div className="p-6">
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}

export default function ProjectsPage() {
  const [filters, setFilters] = useState<ProjectFilters>({
    page: 1,
    page_size: 25,
    ordering: '-start_date',
  })
  
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)

  // API Hooks
  const { data: projectsData, isLoading, error, refetch } = useProjects(filters)
  const createProjectMutation = useCreateProject()
  const updateProjectMutation = useUpdateProject()
  const deleteProjectMutation = useDeleteProject()

  // Handle filter changes
  const handleFiltersChange = (newFilters: Partial<ProjectFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 })) // Reset to page 1 when filters change
  }

  const handleClearFilters = () => {
    setFilters({
      page: 1,
      page_size: 25,
      ordering: '-start_date',
    })
  }

  // CRUD Operations
  const handleCreateProject = async (data: ProjectFormData) => {
    try {
      await createProjectMutation.mutateAsync(data)
      setIsCreateModalOpen(false)
      refetch()
    } catch (error) {
      // Error is handled by the mutation
    }
  }

  const handleEditProject = (project: Project) => {
    setSelectedProject(project)
    setIsEditModalOpen(true)
  }

  const handleUpdateProject = async (data: ProjectFormData) => {
    if (!selectedProject) return
    
    try {
      await updateProjectMutation.mutateAsync({ id: selectedProject.id, data })
      setIsEditModalOpen(false)
      setSelectedProject(null)
      refetch()
    } catch (error) {
      // Error is handled by the mutation
    }
  }

  const handleDeleteProject = async (project: Project) => {
    if (!confirm(`هل أنت متأكد من حذف المشروع "${project.name}"؟`)) {
      return
    }

    try {
      await deleteProjectMutation.mutateAsync(project.id)
      refetch()
    } catch (error) {
      // Error is handled by the mutation
    }
  }

  const handleViewProject = (project: Project) => {
    // TODO: Navigate to project details page
    toast.success(`عرض تفاصيل المشروع: ${project.name}`)
  }

  const handleExportProjects = () => {
    // TODO: Implement export functionality
    toast.success('سيتم تنفيذ تصدير المشاريع قريباً')
  }

  // Handle API errors
  useEffect(() => {
    if (error) {
      toast.error('حدث خطأ في تحميل المشاريع')
    }
  }, [error])

  return (
    <MainLayout>
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">إدارة المشاريع</h1>
              <p className="mt-1 text-sm text-gray-500">
                إدارة شاملة لجميع المشاريع والعمليات
              </p>
            </div>
            <div className="flex items-center space-x-3 space-x-reverse">
              <Button
                variant="outline"
                onClick={handleExportProjects}
                className="flex items-center space-x-2 space-x-reverse"
              >
                <DocumentArrowDownIcon className="h-4 w-4" />
                <span>تصدير</span>
              </Button>
              <Button
                onClick={() => setIsCreateModalOpen(true)}
                className="flex items-center space-x-2 space-x-reverse"
              >
                <PlusIcon className="h-4 w-4" />
                <span>مشروع جديد</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Filters */}
          <ProjectsFilter
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onClearFilters={handleClearFilters}
          />

          {/* Stats Cards */}
          {projectsData && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-surface p-6 rounded-lg shadow-sm border border-border-light">
                <div className="flex items-center">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-text-secondary">إجمالي المشاريع</p>
                    <p className="text-2xl font-bold text-text-primary">{projectsData.count}</p>
                  </div>
                  <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="bg-surface p-6 rounded-lg shadow-sm border border-border-light">
                <div className="flex items-center">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-text-secondary">قيد التنفيذ</p>
                    <p className="text-2xl font-bold text-primary-600">
                      {projectsData.results.filter(p => p.status === 'in_progress').length}
                    </p>
                  </div>
                  <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="bg-surface p-6 rounded-lg shadow-sm border border-border-light">
                <div className="flex items-center">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-text-secondary">مكتملة</p>
                    <p className="text-2xl font-bold text-accent-green-600">
                      {projectsData.results.filter(p => p.status === 'completed').length}
                    </p>
                  </div>
                  <div className="w-8 h-8 bg-accent-green-50 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-accent-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="bg-surface p-6 rounded-lg shadow-sm border border-border-light">
                <div className="flex items-center">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-text-secondary">متأخرة</p>
                    <p className="text-2xl font-bold text-accent-red-600">
                      {projectsData.results.filter(p =>
                        new Date(p.expected_end_date) < new Date() && p.status !== 'completed'
                      ).length}
                    </p>
                  </div>
                  <div className="w-8 h-8 bg-accent-red-50 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-accent-red-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Projects Table */}
          <div className="bg-surface rounded-lg shadow-sm border border-border-light">
            <ProjectsTable
              projects={projectsData?.results || []}
              loading={isLoading}
              onEdit={handleEditProject}
              onDelete={handleDeleteProject}
              onView={handleViewProject}
            />
          </div>
        </div>
      </div>

      {/* Create Project Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="إنشاء مشروع جديد"
      >
        <ProjectForm
          onSubmit={handleCreateProject}
          onCancel={() => setIsCreateModalOpen(false)}
          loading={createProjectMutation.isPending}
        />
      </Modal>

      {/* Edit Project Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false)
          setSelectedProject(null)
        }}
        title="تعديل المشروع"
      >
        {selectedProject && (
          <ProjectForm
            project={selectedProject}
            onSubmit={handleUpdateProject}
            onCancel={() => {
              setIsEditModalOpen(false)
              setSelectedProject(null)
            }}
            loading={updateProjectMutation.isPending}
          />
        )}
      </Modal>
    </MainLayout>
  )
}
